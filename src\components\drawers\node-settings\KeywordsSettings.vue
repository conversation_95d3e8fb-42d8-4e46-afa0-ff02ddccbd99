<template>
  <div class="keywords-settings">
    <div class="form-section">
      <h4>节点标题</h4>
      <el-input 
        v-model="nodeData.name" 
        placeholder="节点标题" 
        style="margin-bottom: 15px"
      ></el-input>
    </div>
    
    <div class="form-section">
      <h4>输入</h4>
      <div class="input-variable-box">
        <div v-for="(variable, index) in nodeData.variables" :key="index" class="variable-item">
          <el-tag size="medium">输入</el-tag>
          <span class="variable-name">{{ variable.name }}@{{ variable.source }}</span>
          <el-button 
            type="text" 
            icon="el-icon-delete" 
            class="delete-variable-btn"
            @click="deleteVariable(index)"
          ></el-button>
        </div>
        <div class="add-variable-button">
          <el-button type="text" icon="el-icon-plus" @click="showAddVariableDialog">新增变量</el-button>
        </div>
      </div>
    </div>
    
    <div class="form-section">
      <h4>选择使用的模型</h4>
      <el-select
        v-model="nodeData.form.llm_id"
        placeholder="选择模型"
        style="width: 100%"
        @change="handleModelChange"
      >
        <el-option label="qwen-max@Tongyi-Qianwen" value="qwen-max@Tongyi-Qianwen"></el-option>
        <el-option label="GPT-4" value="GPT-4"></el-option>
        <el-option label="Claude 3" value="Claude 3"></el-option>
        <el-option label="Llama 3" value="Llama 3"></el-option>
        <el-option label="Gemini" value="Gemini"></el-option>
      </el-select>
    </div>
    
    <div class="form-section">
      <h4>查询输入源</h4>
      <div style="display: flex; gap: 8px;">
        <el-select v-model="selectedQueryComponentId" placeholder="选择查询输入来源" style="flex: 1;">
          <el-option
            v-for="component in availableComponents"
            :key="component.id"
            :label="component.label"
            :value="component.id">
          </el-option>
        </el-select>
        <el-button
          size="small"
          icon="el-icon-refresh"
          @click="refreshComponents"
          :loading="refreshing"
          title="刷新组件列表">
        </el-button>
      </div>
      <div class="form-item-help">
        <i class="el-icon-info"></i>
        <span>选择为关键词提取提供查询内容的上游组件</span>
        <span v-if="availableComponents.length === 0" style="color: #f56c6c; margin-left: 8px;">
          (未找到组件，请点击刷新按钮)
        </span>
      </div>
    </div>

    <div class="form-section">
      <h4>Top N</h4>
      <el-slider v-model="nodeData.form.top_n" :min="1" :max="10" :step="1"></el-slider>
    </div>



    <div class="form-section">
      <h4>温度 (Temperature)</h4>
      <el-switch v-model="nodeData.form.temperatureEnabled" style="margin-bottom: 10px;"></el-switch>
      <el-slider
        v-if="nodeData.form.temperatureEnabled"
        v-model="nodeData.form.temperature"
        :min="0"
        :max="2"
        :step="0.1"
        :disabled="!nodeData.form.temperatureEnabled"
      ></el-slider>
    </div>

    <div class="form-section">
      <h4>最大令牌数 (Max Tokens)</h4>
      <el-switch v-model="nodeData.form.maxTokensEnabled" style="margin-bottom: 10px;"></el-switch>
      <el-input-number
        v-if="nodeData.form.maxTokensEnabled"
        v-model="nodeData.form.max_tokens"
        :min="1"
        :max="4096"
        :disabled="!nodeData.form.maxTokensEnabled"
        style="width: 100%;"
      ></el-input-number>
    </div>
  </div>
</template>

<script>
export default {
  name: 'KeywordsSettings',
  props: {
    initialData: {
      type: Object,
      default: () => ({
        name: '关键词',
        selectedModel: '默认大模型',
        topN: 5,
        variables: [
          { name: 'deepseek-chat', source: 'DeepSeek' }
        ]
      })
    },
    graphData: {
      type: Object,
      default: () => null
    }
  },
  data() {
    return {
      nodeData: {
        name: '',
        selectedModel: '默认大模型',
        topN: 5,
        variables: []
      },
      showVariableDialog: false,
      selectedQueryComponentId: '', // 当前选择的查询组件ID
      availableComponents: [], // 可用的组件列表
      refreshing: false // 是否正在刷新组件列表
    };
  },
  mounted() {
    // 组件挂载后加载可用组件列表
    this.loadAvailableComponents();

    // 如果第一次加载失败，延迟重试
    if (this.availableComponents.length === 0) {
      setTimeout(() => {
        console.log('[KeywordsSettings] 延迟重试加载组件列表...');
        this.loadAvailableComponents();
      }, 500);

      // 再次重试
      setTimeout(() => {
        if (this.availableComponents.length === 0) {
          console.log('[KeywordsSettings] 第二次延迟重试加载组件列表...');
          this.loadAvailableComponents();
        }
      }, 1000);
    }
  },
  watch: {
    initialData: {
      handler(newVal) {
        console.log('[KeywordsSettings] 接收到initialData:', newVal);
        console.log('[KeywordsSettings] newVal.form?.query:', newVal.form?.query);

        this.nodeData = {
          name: newVal.name || '关键词',
          selectedModel: newVal.selectedModel || newVal.form?.llm_id || 'qwen-max@Tongyi-Qianwen',
          topN: newVal.topN || newVal.form?.top_n || 5,
          variables: Array.isArray(newVal.variables) ? [...newVal.variables] : [
            { name: 'deepseek-chat', source: 'DeepSeek' }
          ],
          form: {
            frequencyPenaltyEnabled: newVal.form?.frequencyPenaltyEnabled || false,
            frequency_penalty: newVal.form?.frequency_penalty || 0.7,
            llm_id: newVal.form?.llm_id || newVal.selectedModel || "qwen-max@Tongyi-Qianwen",
            maxTokensEnabled: newVal.form?.maxTokensEnabled || false,
            max_tokens: newVal.form?.max_tokens || 256,
            presencePenaltyEnabled: newVal.form?.presencePenaltyEnabled || false,
            presence_penalty: newVal.form?.presence_penalty || 0.4,
            query: newVal.form?.query || [],
            temperature: newVal.form?.temperature || 0.1,
            temperatureEnabled: newVal.form?.temperatureEnabled || false,
            topPEnabled: newVal.form?.topPEnabled || false,
            top_n: newVal.form?.top_n || newVal.topN || 5,
            top_p: newVal.form?.top_p || 0.3
          }
        };

        // 初始化选中的查询组件ID
        console.log('[KeywordsSettings] 初始化query，当前query:', this.nodeData.form.query);
        if (this.nodeData.form.query && this.nodeData.form.query.length > 0) {
          this.selectedQueryComponentId = this.nodeData.form.query[0].component_id || '';
          console.log('[KeywordsSettings] 设置selectedQueryComponentId为:', this.selectedQueryComponentId);
        } else {
          this.selectedQueryComponentId = '';
          console.log('[KeywordsSettings] query为空，selectedQueryComponentId设置为空');
        }

        // 获取可用组件列表
        this.loadAvailableComponents();
      },
      immediate: true,
      deep: true
    },
    nodeData: {
      handler(newVal) {
        this.$emit('update', newVal);
      },
      deep: true
    },
    selectedQueryComponentId: {
      handler(newVal) {
        console.log('[KeywordsSettings] selectedQueryComponentId变化:', newVal);
        // 当选择的查询组件改变时，更新nodeData.form.query
        if (newVal) {
          this.nodeData.form.query = [{
            component_id: newVal,
            type: "reference"
          }];
          console.log('[KeywordsSettings] 更新query为:', this.nodeData.form.query);
        } else {
          this.nodeData.form.query = [];
          console.log('[KeywordsSettings] 清空query');
        }
      }
    },
    graphData: {
      handler(newVal) {
        // 当图表数据变化时，重新加载可用组件
        if (newVal) {
          this.loadAvailableComponents();
        }
      },
      deep: true
    }
  },
  methods: {
    // 获取可用的组件列表
    loadAvailableComponents() {
      console.log('[KeywordsSettings] 开始加载可用组件...');
      console.log('[KeywordsSettings] 传入的graphData:', this.graphData);

      try {
        // 优先使用传入的graphData，否则尝试获取
        let graphData = this.graphData;
        if (!graphData) {
          console.log('[KeywordsSettings] 传入的graphData为空，尝试手动获取...');
          graphData = this.getGraphData();
          console.log('[KeywordsSettings] 手动获取的graphData:', graphData);
        }

        if (graphData && graphData.nodes) {
          console.log('[KeywordsSettings] 图表节点数据:', graphData.nodes);
          this.availableComponents = this.extractComponentsFromGraph(graphData);
          console.log('[KeywordsSettings] 提取到的可用组件:', this.availableComponents);
        } else {
          console.warn('[KeywordsSettings] 无法获取图表数据，graphData:', graphData);
          this.availableComponents = [];
        }
      } catch (error) {
        console.error('[KeywordsSettings] 获取组件列表失败:', error);
        this.availableComponents = [];
      }
    },

    // 获取图表数据
    getGraphData() {
      console.log('[KeywordsSettings] 尝试获取图表数据...');

      // 方案1: 通过NodeDrawer的父组件获取
      if (this.$parent && this.$parent.$parent && this.$parent.$parent.$refs && this.$parent.$parent.$refs.x6Graph) {
        console.log('[KeywordsSettings] 方案1: 通过$parent.$parent.$refs.x6Graph获取');
        const data = this.$parent.$parent.$refs.x6Graph.getGraphData();
        if (data) return data;
      }

      // 方案2: 直接通过父组件获取
      if (this.$parent && this.$parent.$refs && this.$parent.$refs.x6Graph) {
        console.log('[KeywordsSettings] 方案2: 通过$parent.$refs.x6Graph获取');
        const data = this.$parent.$refs.x6Graph.getGraphData();
        if (data) return data;
      }

      // 方案3: 通过根组件获取
      if (this.$root && this.$root.$refs && this.$root.$refs.x6Graph) {
        console.log('[KeywordsSettings] 方案3: 通过$root.$refs.x6Graph获取');
        const data = this.$root.$refs.x6Graph.getGraphData();
        if (data) return data;
      }

      // 方案4: 遍历所有父组件查找x6Graph
      let parent = this.$parent;
      while (parent) {
        if (parent.$refs && parent.$refs.x6Graph) {
          console.log('[KeywordsSettings] 方案4: 通过遍历父组件找到x6Graph');
          const data = parent.$refs.x6Graph.getGraphData();
          if (data) return data;
        }
        parent = parent.$parent;
      }

      // 方案5: 通过事件请求图表数据
      console.log('[KeywordsSettings] 方案5: 通过事件请求图表数据');
      this.$emit('request-graph-data');

      // 方案6: 备用方案：通过全局变量
      if (window.currentGraphData) {
        console.log('[KeywordsSettings] 方案6: 通过全局变量获取');
        return window.currentGraphData;
      }

      console.warn('[KeywordsSettings] 所有方案都失败，无法获取图表数据');
      return null;
    },

    // 从图数据中提取组件信息
    extractComponentsFromGraph(graphData) {
      const components = [];

      if (graphData && graphData.nodes) {
        graphData.nodes.forEach(node => {
          if (node.id && node.data) {
            const componentName = this.getComponentDisplayName(node);
            const displayName = node.data.name || node.data.title || componentName;

            components.push({
              id: node.id,
              label: `${displayName} (${node.id})`
            });
          }
        });
      }

      return components;
    },

    // 获取组件显示名称
    getComponentDisplayName(node) {
      const type = node.type || node.data?.type;
      const typeMap = {
        'beginNode': 'Begin',
        'keywordNode': 'KeywordExtract',
        'keywords': 'KeywordExtract',
        'categorizeNode': 'Categorize',
        'classification': 'Categorize',
        'retrievalNode': 'Retrieval',
        'retrieval': 'Retrieval',
        'generateNode': 'Generate',
        'generation': 'Generate',
        'logicNode': 'Answer',
        'dialogue': 'Answer',
        'messageNode': 'Message',
        'message': 'Message'
      };
      return typeMap[type] || type || 'Unknown';
    },

    // 手动刷新组件列表
    async refreshComponents() {
      this.refreshing = true;
      console.log('[KeywordsSettings] 手动刷新组件列表...');

      try {
        // 等待一小段时间确保图表已经渲染
        await new Promise(resolve => setTimeout(resolve, 100));
        this.loadAvailableComponents();

        if (this.availableComponents.length > 0) {
          this.$message.success(`成功加载 ${this.availableComponents.length} 个组件`);
        } else {
          this.$message.warning('未找到可用组件，请确保图表中有其他节点');
        }
      } catch (error) {
        console.error('[KeywordsSettings] 刷新组件列表失败:', error);
        this.$message.error('刷新组件列表失败');
      } finally {
        this.refreshing = false;
      }
    },

    // 更新图表数据（由父组件调用）
    updateGraphData(graphData) {
      console.log('[KeywordsSettings] 收到图表数据更新:', graphData);
      if (graphData && graphData.nodes) {
        this.availableComponents = this.extractComponentsFromGraph(graphData);
        console.log('[KeywordsSettings] 更新后的可用组件:', this.availableComponents);
      }
    },

    handleModelChange(model) {
      // 同时更新 selectedModel 和 form.llm_id
      this.nodeData.selectedModel = model;
      this.nodeData.form.llm_id = model;
      this.$emit('model-change', model);
    },


    
    showAddVariableDialog() {
      this.$emit('show-variable-dialog');
    },
    
    deleteVariable(index) {
      this.$confirm('确定要删除此变量吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 从列表中删除
        this.nodeData.variables.splice(index, 1);
        
        this.$message({
          type: 'success',
          message: '变量已删除'
        });
      }).catch(() => {
        // 取消删除
      });
    },
    
    addVariable(variable) {
      this.nodeData.variables.push(variable);
    }
  }
}
</script>

<style scoped>
.form-section {
  margin-bottom: 24px;
}

.form-section h4 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 16px;
  color: #333;
}

/* 关键词节点相关样式 */
.input-variable-box {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 16px;
  background-color: #f9f9f9;
}

.variable-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  position: relative;
  padding: 5px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.variable-item:hover {
  background-color: #f0f0f0;
}

.variable-name {
  margin-left: 12px;
  font-size: 14px;
  color: #606266;
  flex-grow: 1;
}

.delete-variable-btn {
  padding: 2px;
  margin-left: 8px;
  color: #909399;
}

.delete-variable-btn:hover {
  color: #F56C6C;
}

.add-variable-button {
  display: flex;
  justify-content: center;
  margin-top: 8px;
}

.form-item-help {
  margin-top: 5px;
  font-size: 12px;
  color: #909399;
  display: flex;
  align-items: center;
}

.form-item-help i {
  margin-right: 4px;
}
</style>