<template>
  <div id="app">
    <NavHeader
      @menu-select="handleMenuSelect"
      @run-workflow="handleRunWorkflow"
      @save-flow="handleSave"
      :is-saving="isSaving"
    />
    <!-- <div style="padding: 8px 24px; font-size: 18px; font-weight: bold; color: #6366F1;">当前工作流：{{ currentAgentName }}</div> -->
    <div class="main-container">
      <!-- Agent管理页面 -->
      <div v-if="currentPage === 'agent'" class="agent-page">
        <AgentManagement @edit-agent="handleEditAgent" />
      </div>

      <!-- 工作流页面 -->
      <div v-else class="workflow-page">
        <div class="sidebar-container" :class="{ 'collapsed': sidebarCollapsed }">
          <ModelSelector @drag-start="onModelDragStart" @show-tooltip="showGlobalTooltip" @hide-tooltip="hideGlobalTooltip" />
          <div class="sidebar-toggle" @click="toggleSidebar">
            <i :class="[sidebarCollapsed ? 'el-icon-arrow-right' : 'el-icon-arrow-left']"></i>
          </div>
        </div>

        <!-- 工具栏已移除 -->

        <X6Graph
          ref="x6Graph"
          :agentName="currentAgentName"
          @edit-comment="showCommentEditor"
          @edit-loop="showLoopEditor"
          @edit-iteration-item="showIterationItemEditor"
          @edit-classification="showClassificationEditor"
          @edit-template="showTemplateEditor"
          @edit-condition="showConditionEditor"
          @node-selected="handleNodeSelected"
        />
      </div>
    </div>

    <!-- 全局提示框 -->
    <GlobalTooltip
      :visible="globalTooltip.visible"
      :tooltipStyles="globalTooltip.style"
      :content="globalTooltip.content"
    />

    <!-- 注释编辑弹窗 -->
    <CommentDialog
      :visible="commentDialogVisible"
      :commentData="currentComment"
      @close="commentDialogVisible = false"
    />

    <!-- 循环节点编辑弹窗 -->
    <LoopDialog
      :visible="loopDialogVisible"
      :loopData="currentLoop"
      @close="loopDialogVisible = false"
    />

    <!-- 循环项编辑弹窗 -->
    <IterationItemDialog
      :visible="iterationItemDialogVisible"
      :itemData="currentIterationItem"
      @close="iterationItemDialogVisible = false"
    />

    <!-- 问题分类编辑弹窗 -->
    <ClassificationDialog
      :visible="classificationDialogVisible"
      :classificationData="currentClassification"
      @close="classificationDialogVisible = false"
    />

    <!-- 节点属性抽屉 -->
    <NodeDrawer
      ref="nodeDrawer"
      :visible="nodeDrawerVisible"
      :node="selectedNode"
      @close="closeNodeDrawer"
      @closed="handleDrawerClosed"
      @save="saveNodeSettings"
      @show-variable-dialog="showAddVariableDialog"
      @edit-classification="showClassificationEditor"
    />

    <!-- 添加变量弹窗 -->
    <VariableDialog
      :visible="addVariableDialogVisible"
      @close="addVariableDialogVisible = false"
      @add="addNewVariable"
    />

    <!-- 模板转换编辑弹窗 -->
    <TemplateDialog
      :visible="templateDialogVisible"
      :templateData="currentTemplate"
      @close="templateDialogVisible = false"
    />

    <!-- 条件编辑弹窗已移除，使用 NodeDrawer 和 ConditionSettings 组件 -->

    <!-- AI对话抽屉 -->
    <ChatDrawer
      :visible="chatDrawerVisible"
      :agentId="currentAgentId"
      :workflowDSL="currentWorkflowDSL"
      @close="chatDrawerVisible = false"
    />
  </div>
</template>

<script>
import X6Graph from './components/X6Graph.vue'
import NavHeader from './components/NavHeader.vue'
import ModelSelector from './components/ModelSelector.vue'
import GlobalTooltip from './components/common/GlobalTooltip.vue'
import CommentDialog from './components/dialogs/CommentDialog.vue'
import LoopDialog from './components/dialogs/LoopDialog.vue'
import IterationItemDialog from './components/dialogs/IterationItemDialog.vue'
import ClassificationDialog from './components/dialogs/ClassificationDialog.vue'
import VariableDialog from './components/dialogs/VariableDialog.vue'
import TemplateDialog from './components/dialogs/TemplateDialog.vue'
import NodeDrawer from './components/drawers/NodeDrawer.vue'
import AgentManagement from './components/AgentManagement.vue'
import ChatDrawer from './components/drawers/ChatDrawer.vue'
import { updateAgent } from './api/agent-management-API';
import { extractKeywords } from './api/index.js';
import { debugDSLSave, checkConversationIntegrity } from './utils/dslDebugger';

// 🔥 删除了getPlaceholderContent函数，因为我们不再使用inputs字段

// 创建默认输出结构
function createDefaultOutput(componentName, nodeParams = {}, nodeData = {}) {
  switch (componentName) {
    case 'Begin': {
      // 优先从节点的output.content中读取，然后是form.prologue，最后是params.prologue
      let beginContent = "Hi! I'm your smart assistant. What can I do for you?";

      if (nodeData.output && nodeData.output.content && nodeData.output.content['0'] && nodeData.output.content['0'].content) {
        beginContent = nodeData.output.content['0'].content;
      } else if (nodeData.form && nodeData.form.prologue) {
        beginContent = nodeData.form.prologue;
      } else if (nodeParams.prologue) {
        beginContent = nodeParams.prologue;
      }

      return {
        content: {
          "0": {
            content: beginContent
          }
        }
      };
    }
    case 'Answer':
      return {
        content: "",
        reference: []
      };
    case 'Generate':
      return {
        content: {
          "0": {
            content: "",
            reference: []
          }
        }
      };
    case 'Retrieval':
      return {
        component_id: {
          "0": ""
        },
        content: {
          "0": ""
        }
      };
    case 'Categorize':
      return {
        content: {
          "0": ""
        }
      };
    case 'KeywordExtract':
      return {
        component_id: {
          "0": ""
        },
        content: {
          "0": ""
        }
      };
    default:
      return null;
  }
}

// 创建运行时参数结构
function createRuntimeParams(componentName, nodeParams, nodeData = {}) {
  // 根据组件类型创建特定的运行时参数
  switch (componentName) {
    case 'Begin':
      // Begin组件使用简化的参数结构，符合RAGFlow标准格式
      return {
        prologue: nodeParams.prologue || "Hi! I'm your smart assistant. What can I do for you?"
      };

    case 'Answer':
      // Answer组件使用简化的参数结构，符合RAGFlow标准格式
      return {};

    case 'Generate':
      // Generate组件使用简化的参数结构，符合标准格式
      return {
        cite: nodeParams.cite !== undefined ? nodeParams.cite : true,
        llm_id: nodeParams.llm_id || "qwen-max@Tongyi-Qianwen",
        message_history_window_size: nodeParams.message_history_window_size || 12,
        parameters: nodeParams.parameters || [],
        prompt: nodeParams.prompt || "请总结以下段落。注意数字，不要胡编乱造。段落如下：\n{input}\n以上就是你需要总结的内容。"
      };

    case 'Categorize': {
      // Categorize组件使用简化的参数结构，符合RAGFlow标准格式
      // 🔥 现在components使用图表节点ID，不需要转换
      console.log(`[DSL构建] Categorize组件使用图表节点ID:`, nodeParams.category_description);

      return {
        category_description: nodeParams.category_description || {},
        llm_id: nodeParams.llm_id || "qwen-max@Tongyi-Qianwen",
        message_history_window_size: 1,
        query: []
      };
    }

    case 'Retrieval':
      // Retrieval组件使用简化的参数结构，符合标准格式
      return {
        kb_ids: nodeParams.kb_ids || [],
        keywords_similarity_weight: nodeParams.keywords_similarity_weight || 0.3,
        query: [],
        similarity_threshold: nodeParams.similarity_threshold || 0.2,
        top_n: nodeParams.top_n || 8,
        use_kg: nodeParams.use_kg || false
      };

    case 'KeywordExtract': {
      // KeywordExtract组件使用专用的参数结构，符合RAGFlow标准格式
      console.log('[createRuntimeParams] KeywordExtract 输入的nodeParams:', nodeParams);
      console.log('[createRuntimeParams] KeywordExtract nodeParams.query:', nodeParams.query);
      console.log('[createRuntimeParams] KeywordExtract nodeParams.query类型:', typeof nodeParams.query);
      console.log('[createRuntimeParams] KeywordExtract nodeParams.query长度:', nodeParams.query?.length);

      const result = {
        frequencyPenaltyEnabled: nodeParams.frequencyPenaltyEnabled || false,
        frequency_penalty: nodeParams.frequency_penalty || 0.7,
        llm_id: nodeParams.llm_id || "qwen-max@Tongyi-Qianwen",
        maxTokensEnabled: nodeParams.maxTokensEnabled || false,
        max_tokens: nodeParams.max_tokens || 256,
        presencePenaltyEnabled: nodeParams.presencePenaltyEnabled || false,
        presence_penalty: nodeParams.presence_penalty || 0.4,
        query: nodeParams.query || [],
        temperature: nodeParams.temperature || 0.1,
        temperatureEnabled: nodeParams.temperatureEnabled || false,
        topPEnabled: nodeParams.topPEnabled || false,
        top_n: nodeParams.top_n || 5,
        top_p: nodeParams.top_p || 0.3
      };

      console.log('[createRuntimeParams] KeywordExtract 生成的result.query:', result.query);
      return result;
    }

    default:
      return {
        debug_inputs: [],
        infor_var_name: "infor",
        inputs: [],
        message_history_window_size: nodeParams.message_history_window_size || 22,
        output: createDefaultOutput(componentName, nodeParams, nodeData),
        output_var_name: "output",
        query: []
      };
  }
}

// 将 buildComponentsFromGraph 移到 methods 外部
async function buildComponentsFromGraph(graphData, context = {}) {
  const components = {};
  if (!graphData || !graphData.nodes || !graphData.edges) return components;



  // 节点类型到组件名称的映射
  const getComponentName = (node) => {
    // 基于节点类型映射，不依赖可能错误的label
    const typeMapping = {
      'beginNode': 'Begin',
      'logicNode': node.data?.name?.includes('对话') ? 'Answer' : 'Concentrator',
      'retrievalNode': 'Retrieval',
      'generateNode': 'Generate',
      'categorizeNode': 'Categorize',
      'messageNode': 'Message',
      'rewriteNode': 'RewriteQuestion',
      'keywordNode': 'KeywordExtract',
      'switchNode': 'Switch',
      'templateNode': 'Template',
      'group': 'Iteration',
      'ragNode': 'Code',
      'iterationStartNode': 'IterationItem',
      'noteNode': 'Note'
    };

    return typeMapping[node.type] || node.type;
  };

  // 获取节点参数，过滤掉不需要的字段
  const getNodeParams = (node) => {
    const params = { ...node.data?.form } || {};

    // 移除UI相关的字段，只保留业务参数
    const uiFields = ['temperatureEnabled', 'maxTokensEnabled', 'presencePenaltyEnabled', 'frequencyPenaltyEnabled', 'topPEnabled'];
    uiFields.forEach(field => {
      delete params[field];
    });

    // 确保特定节点类型有必需的字段
    const componentName = getComponentName(node);

    // 根据组件类型设置必需的默认字段
    switch (componentName) {
      case 'Switch':
        if (!params.conditions) {
          params.conditions = [];
        }
        break;
      case 'Message':
        if (!params.messages) {
          params.messages = [];
        }
        break;
      case 'Categorize':
        if (!params.category_description) {
          params.category_description = {};
        }
        if (!params.query) {
          params.query = [];
        }
        break;
      case 'Retrieval':
        if (!params.query) {
          params.query = [];
        }
        break;
      case 'Generate':
        if (!params.parameters) {
          params.parameters = [];
        }
        break;
      case 'KeywordExtract':
        console.log('[getNodeParams] KeywordExtract处理前 params.query:', params.query);
        console.log('[getNodeParams] KeywordExtract !params.query:', !params.query);
        if (!params.query) {
          console.log('[getNodeParams] KeywordExtract 重置query为空数组');
          params.query = [];
        } else {
          console.log('[getNodeParams] KeywordExtract 保持原有query:', params.query);
        }
        break;
    }

    return params;
  };

  // 创建节点ID映射表（原始ID -> 新的标准ID）
  const nodeIdMap = {};

  // 1. 为每个节点生成新的ID（使用图表节点ID格式）
  graphData.nodes.forEach(node => {
    // 🔥 直接使用图表节点ID，不生成随机名称
    nodeIdMap[node.id] = node.id;
  });

  // 2. 初始化每个节点
  graphData.nodes.forEach(node => {
    const componentName = getComponentName(node);
    const nodeParams = getNodeParams(node);
    const newNodeId = nodeIdMap[node.id];

    // 调试信息
    if (node.id.includes('Code') || node.id.includes('Retrieval') || node.id.includes('KeywordExtract')) {
      console.log(`[DSL生成] 节点${node.id}:`, {
        type: node.type,
        label: node.data?.label,
        component_name: componentName,
        nodeData: node.data,
        nodeParams: nodeParams,
        formQuery: node.data?.form?.query
      });
    }

    // 创建完整的运行时数据结构
    const runtimeParams = createRuntimeParams(componentName, nodeParams, node.data);

    // 为KeywordExtract添加特殊调试
    if (componentName === 'KeywordExtract') {
      console.log('[DSL构建] KeywordExtract createRuntimeParams返回值:', runtimeParams);
      console.log('[DSL构建] KeywordExtract runtimeParams.query:', runtimeParams.query);
    }

    const componentData = {
      downstream: [],
      upstream: [],
      obj: {
        component_name: componentName,
        params: runtimeParams
      }
    };

    // 再次检查componentData
    if (componentName === 'KeywordExtract') {
      console.log('[DSL构建] KeywordExtract 最终componentData:', componentData);
      console.log('[DSL构建] KeywordExtract 最终componentData.obj.params.query:', componentData.obj.params.query);
    }

    // 🔥 根据上下文决定是否创建inputs和output字段
    // 如果是对话后的状态，可能需要这些字段；如果是初始状态，则不需要
    if (context.isAfterConversation) {
      // 对话后状态：创建基本的inputs和output结构
      componentData.obj.inputs = [];
      componentData.obj.output = createDefaultOutput(componentName, nodeParams, node.data);
      console.log(`[DSL构建] 对话后状态：为 ${componentName} 创建inputs和output字段`);
    } else {
      // 初始状态：不创建inputs和output字段，与RAGFlow原生一致
      console.log(`[DSL构建] 初始状态：${componentName} 不创建inputs和output字段`);
    }

    // 不再为Retrieval组件添加固定的测试inputs，这将在clearRuntimeData中处理

    // 为循环项节点添加 parent_id
    if (node.type === 'iterationStartNode' && node.parentId) {
      componentData.parent_id = nodeIdMap[node.parentId] || node.parentId;
    }

    components[newNodeId] = componentData;

    // 为KeywordExtract添加赋值后的检查
    if (componentName === 'KeywordExtract') {
      console.log('[DSL构建] KeywordExtract 赋值到components后:', components[newNodeId]);
      console.log('[DSL构建] KeywordExtract 赋值后query:', components[newNodeId].obj.params.query);
    }
  });

  // 3. 遍历所有边，填充上下游（使用新的节点ID）
  graphData.edges.forEach(edge => {
    const originalSource = edge.source?.cell || edge.source;
    const originalTarget = edge.target?.cell || edge.target;

    const newSource = nodeIdMap[originalSource];
    const newTarget = nodeIdMap[originalTarget];

    if (components[newSource] && components[newTarget]) {
      // 避免重复添加相同的连接
      if (!components[newSource].downstream.includes(newTarget)) {
        components[newSource].downstream.push(newTarget);
      }
      if (!components[newTarget].upstream.includes(newSource)) {
        components[newTarget].upstream.push(newSource);
      }
    }
  });

  // 4. 处理Categorize组件的category_description中的to字段
  updateCategorizeComponentsRouting(components, graphData, nodeIdMap);

  // 5. 检测并修复循环依赖
  fixCircularDependencies(components);

  // 6. 建立明确的组件输入连接
  clearRuntimeData(components, context);

  // 7. 验证KeywordExtract组件输出格式
  validateKeywordExtractOutputs(components);

  // 8. 专门验证Categorize组件输入
  validateCategorizeInputs(components);

  // 9. 暂时禁用动态更新机制，专注于解决基础问题
  console.log('[DSL构建] 跳过动态更新机制，使用静态验证');

  // 执行基础的静态验证和修复
  Object.keys(components).forEach(nodeId => {
    const component = components[nodeId];
    const componentName = component.obj.component_name;

    // 🔥 不再为组件添加inputs字段，让RAGFlow根据upstream/downstream自动推断
    if (componentName !== 'Begin') {
      console.log(`[DSL构建] ${nodeId} 跳过inputs创建，依赖upstream连接: ${component.upstream?.join(', ') || '无'}`);
    }

    // 🔥 不再验证inputs结构，因为我们不使用inputs字段
  });

  // 10. 专门修复可能导致"list index out of range"的问题

  // 修复每个组件的结构，确保不会导致后端数组越界
  Object.keys(components).forEach(nodeId => {
    const component = components[nodeId];
    const componentName = component.obj.component_name;

    console.log(`[DSL构建] 检查组件 ${nodeId} (${componentName})`);

    // 🔥 只确保params结构存在，不创建inputs和output
    if (!component.obj.params) {
      component.obj.params = {};
    }

    // 🔥 关键修复：移除inputs字段，RAGFlow不需要这个字段！
    // 根据RAGFlow官方DSL格式，组件不应该有inputs字段
    // 组件连接关系通过upstream/downstream处理
    if (component.obj.inputs) {
      delete component.obj.inputs;
      console.log(`[DSL构建] 删除 ${nodeId} 的inputs字段`);
    }

    // 🔥 关键修复：为Retrieval组件添加必需的query参数
    if (componentName === 'Retrieval') {
      // 确保params存在
      if (!component.obj.params) {
        component.obj.params = {};
      }

      // 获取上游组件ID（通常是Categorize或其他组件）
      const upstreamId = component.upstream && component.upstream.length > 0 ? component.upstream[0] : 'begin';

      // 🔥 修复参数含义：kb_vars是输入来源（选择组件），query是查询内容（手动输入，可选）
      if (!component.obj.params.kb_vars) {
        component.obj.params.kb_vars = [
          {
            component_id: upstreamId,
            type: "reference"
          }
        ];
      }

      // query是查询内容，如果用户没有手动输入，则不设置（可选参数）
      // 注意：这里不应该自动设置query，应该由用户在UI中输入
      if (component.obj.params.query && Array.isArray(component.obj.params.query)) {
        // 如果query被错误地设置为数组格式，清除它
        delete component.obj.params.query;
      }

      // 设置其他默认参数，使用更安全的值
      if (!component.obj.params.kb_ids || component.obj.params.kb_ids.length === 0) {
        // 不设置默认kb_ids，让用户必须选择知识库
        console.warn(`[DSL构建] ${nodeId} 警告：未设置知识库ID，可能导致错误`);
      }

      // 设置默认参数
      if (component.obj.params.keywords_similarity_weight === undefined) {
        component.obj.params.keywords_similarity_weight = 0.3;
      }
      if (component.obj.params.similarity_threshold === undefined) {
        component.obj.params.similarity_threshold = 0.2;
      }
      if (component.obj.params.top_n === undefined) {
        component.obj.params.top_n = 8;
      }
      if (component.obj.params.use_kg === undefined) {
        component.obj.params.use_kg = false;
      }

      // 设置rerank默认值
      if (component.obj.params.rerank === undefined) {
        component.obj.params.rerank = false;
      }

      // 添加vector_similarity_weight参数
      if (component.obj.params.vector_similarity_weight === undefined) {
        component.obj.params.vector_similarity_weight = 0.7;
      }

      // 🔥 确保kb_vars和query参数正确配置
      if (!component.obj.params.kb_vars) {
        component.obj.params.kb_vars = []; // 知识库变量默认为空
      }
      if (!component.obj.params.query) {
        component.obj.params.query = []; // 查询输入默认为空，在agent模式中会自动配置
      }

    }

    // 🔥 KeywordExtract组件特殊处理
    if (componentName === 'KeywordExtract') {
      // 确保params存在
      if (!component.obj.params) {
        component.obj.params = {};
      }

      // 🔥 关键修复：query参数应该为空数组，不引用其他组件
      component.obj.params.query = [];

      console.log(`[DSL构建] KeywordExtract ${nodeId} query参数已设置为空数组`);
    }

    // 确保所有组件都有params字段
    if (!component.obj.params) {
      component.obj.params = {};
    }

    // 🔥 安全检查输出结构（如果存在的话）
    if (component.obj.output && !component.obj.output.content) {
      component.obj.output.content = { "0": "" };
    }
  });

  // 11. 特别检查可能导致RAGFlow后端数组越界的问题
  console.log('[DSL构建] 检查可能导致RAGFlow后端数组越界的问题');

  Object.keys(components).forEach(nodeId => {
    const component = components[nodeId];
    const componentName = component.obj.component_name;

    // 🔥 不再检查inputs数组，因为我们不使用inputs字段

    // 🔥 不再检查output结构，让RAGFlow完全控制

    // 🔥 安全检查output.content["0"]（如果存在的话）
    if (component.obj.output && component.obj.output.content &&
        !Object.prototype.hasOwnProperty.call(component.obj.output.content, "0")) {
      console.error(`[DSL构建] ${nodeId} output.content["0"]不存在`);
      component.obj.output.content["0"] = "";
    }

    // 检查params结构
    if (!component.obj.params || typeof component.obj.params !== 'object') {
      console.error(`[DSL构建] ${nodeId} params结构无效`);
      component.obj.params = {};
    }

    // 特别检查Categorize组件的category_description，这是最可能导致数组越界的地方
    if (componentName === 'Categorize' && component.obj.params.category_description) {
      const categoryDesc = component.obj.params.category_description;
      const downstreamCount = component.downstream ? component.downstream.length : 0;

      console.log(`[DSL构建] 检查Categorize ${nodeId} 的category_description`);
      console.log(`[DSL构建] 下游组件数量: ${downstreamCount}`);

      // 检查每个分类的index是否在有效范围内
      Object.keys(categoryDesc).forEach(catName => {
        const catConfig = categoryDesc[catName];
        if (typeof catConfig.index === 'number') {
          if (catConfig.index >= downstreamCount || catConfig.index < 0) {
            console.error(`[DSL构建] ${nodeId} 分类"${catName}"的index ${catConfig.index} 超出范围 [0, ${downstreamCount-1}]`);
            // 修复：将index限制在有效范围内
            catConfig.index = Math.max(0, Math.min(catConfig.index, downstreamCount - 1));
            console.log(`[DSL构建] ${nodeId} 分类"${catName}"的index已修复为 ${catConfig.index}`);
          }
        } else {
          console.error(`[DSL构建] ${nodeId} 分类"${catName}"的index不是数字:`, catConfig.index);
          catConfig.index = 0;
        }
      });

      // 如果没有下游组件，清空category_description
      if (downstreamCount === 0) {
        console.warn(`[DSL构建] ${nodeId} 没有下游组件，清空category_description`);
        component.obj.params.category_description = {};
      }
    }
  });

  // 12. 最终验证DSL结构
  console.log('[DSL构建] 最终验证DSL结构');

  const finalValidation = {
    hasBegin: !!components.begin,
    allComponentsHaveValidInputs: true,
    allComponentsHaveValidOutputs: true,
    issues: []
  };

  Object.keys(components).forEach(nodeId => {
    const component = components[nodeId];
    const componentName = component.obj.component_name;

    // 🔥 修正验证逻辑：RAGFlow不使用inputs字段，使用upstream连接
    if (componentName !== 'Begin') {
      // 检查upstream连接而不是inputs
      if (!component.upstream || component.upstream.length === 0) {
        finalValidation.allComponentsHaveValidInputs = false;
        finalValidation.issues.push(`${nodeId} 没有上游连接`);
      }

      // 对于Retrieval组件，还需要检查特殊参数
      if (componentName === 'Retrieval') {
        if (!component.obj.params.kb_vars || component.obj.params.kb_vars.length === 0) {
          finalValidation.allComponentsHaveValidInputs = false;
          finalValidation.issues.push(`${nodeId} 缺少kb_vars参数`);
        }
        if (!component.obj.params.kb_ids || component.obj.params.kb_ids.length === 0) {
          finalValidation.allComponentsHaveValidInputs = false;
          finalValidation.issues.push(`${nodeId} 缺少知识库ID`);
        }
      }
    }

    // 🔥 不再验证输出，让RAGFlow完全控制
  });

  if (finalValidation.issues.length > 0) {
    console.error('[DSL构建] 最终验证发现问题:', finalValidation.issues);
  } else {
    console.log('[DSL构建] ✅ 最终验证通过');
  }

  // 13. 专门检查可能导致RAGFlow后端"list index out of range"的问题
  console.log('[DSL构建] 专门检查可能导致"list index out of range"的问题');

  Object.keys(components).forEach(nodeId => {
    const component = components[nodeId];
    const componentName = component.obj.component_name;

    // 检查Categorize组件的路由配置
    if (componentName === 'Categorize') {
      const categoryDesc = component.obj.params.category_description || {};
      const downstreamIds = component.downstream || [];

      console.log(`[数组越界检查] Categorize ${nodeId}:`);
      console.log(`  下游组件: [${downstreamIds.join(', ')}] (长度: ${downstreamIds.length})`);
      console.log(`  分类配置:`, categoryDesc);

      // 检查每个分类的index和to字段
      Object.keys(categoryDesc).forEach(catName => {
        const catConfig = categoryDesc[catName];

        // 检查index是否在有效范围内
        if (typeof catConfig.index === 'number') {
          if (catConfig.index >= downstreamIds.length) {
            console.error(`[数组越界检查] ❌ ${nodeId} 分类"${catName}" index=${catConfig.index} >= 下游组件数量${downstreamIds.length}`);
            // 强制修复
            catConfig.index = Math.max(0, downstreamIds.length - 1);
            console.log(`[数组越界检查] 🔧 已修复为 index=${catConfig.index}`);
          }
        }

        // 🔥 修复：检查to字段时需要将DSL组件ID转换为图表节点ID进行比较
        if (catConfig.to) {
          // 将下游DSL组件ID列表转换为图表节点ID列表进行比较
          const downstreamGraphNodeIds = downstreamIds.map(dslId => {
            // 从nodeIdMap中找到对应的图表节点ID
            for (const [graphNodeId, mappedDslId] of Object.entries(nodeIdMap)) {
              if (mappedDslId === dslId) {
                return graphNodeId;
              }
            }
            return dslId; // 如果找不到映射，返回原ID
          });

          console.log(`[数组越界检查] 下游图表节点ID: [${downstreamGraphNodeIds.join(', ')}]`);

          if (!downstreamGraphNodeIds.includes(catConfig.to)) {
            console.error(`[数组越界检查] ❌ ${nodeId} 分类"${catName}" to="${catConfig.to}" 不在下游图表节点列表中`);
            // 修复：使用index对应的下游图表节点
            if (catConfig.index >= 0 && catConfig.index < downstreamGraphNodeIds.length) {
              catConfig.to = downstreamGraphNodeIds[catConfig.index];
              console.log(`[数组越界检查] 🔧 已修复为 to="${catConfig.to}"`);
            }
          } else {
            console.log(`[数组越界检查] ✅ ${nodeId} 分类"${catName}" to="${catConfig.to}" 在下游图表节点列表中`);
          }
        }
      });
    }

    // 检查其他可能导致数组越界的配置
    if (component.obj.params) {
      // 检查query数组
      if (component.obj.params.query && Array.isArray(component.obj.params.query)) {
        component.obj.params.query.forEach((queryItem, index) => {
          if (queryItem && typeof queryItem === 'object' && queryItem.component_id) {
            if (!components[queryItem.component_id]) {
              console.error(`[数组越界检查] ❌ ${nodeId} query[${index}] 引用了不存在的组件: ${queryItem.component_id}`);
              // 移除无效的引用
              component.obj.params.query.splice(index, 1);
              console.log(`[数组越界检查] 🔧 已移除无效的query引用`);
            }
          }
        });
      }
    }
  });

  console.log('[DSL构建] ✅ "list index out of range"检查完成');

  // 14. 专门针对agent对话场景的修复

  // 检测是否是agent对话场景（有循环依赖）
  const hasCircularDependency = Object.keys(components).some(nodeId => {
    const component = components[nodeId];
    if (component.obj.component_name === 'KeywordExtract') {
      return component.upstream && component.upstream.some(upId =>
        components[upId] && components[upId].obj.component_name === 'Answer'
      );
    }
    return false;
  });

  if (hasCircularDependency) {
    console.log('[DSL构建] 检测到agent对话场景，应用循环依赖修复');

    // 找到KeywordExtract和Answer组件
    const keywordExtractId = Object.keys(components).find(id =>
      components[id].obj.component_name === 'KeywordExtract'
    );
    const answerId = Object.keys(components).find(id =>
      components[id].obj.component_name === 'Answer'
    );

    if (keywordExtractId && answerId) {
      const keywordComponent = components[keywordExtractId];
      const answerComponent = components[answerId];

      // 🔥 关键修复：移除inputs字段，RAGFlow不需要
      if (keywordComponent.obj.inputs) {
        console.log(`[DSL构建] 🚨 重要修复：移除 ${keywordExtractId} 的inputs字段（agent对话不需要）`);
        delete keywordComponent.obj.inputs;
      }

      if (answerComponent.obj.inputs) {
        console.log(`[DSL构建] 🚨 重要修复：移除 ${answerId} 的inputs字段（agent对话不需要）`);
        delete answerComponent.obj.inputs;
      }

      // 🔥 修正KeywordExtract的upstream连接：只连接Answer，不连接begin
      if (keywordComponent.upstream.includes('begin') && keywordComponent.upstream.includes(answerId)) {
        console.log(`[DSL构建] 🚨 修正 ${keywordExtractId} 的upstream连接：移除begin，只保留${answerId}`);
        keywordComponent.upstream = [answerId];
      }

      // 确保组件有params字段
      if (!keywordComponent.obj.params) {
        keywordComponent.obj.params = {};
      }
      if (!answerComponent.obj.params) {
        answerComponent.obj.params = {};
      }

      // 🔥 在agent对话场景中，确保Retrieval组件正确引用Answer组件
      const retrievalId = Object.keys(components).find(id =>
        components[id].obj.component_name === 'Retrieval'
      );

      if (retrievalId) {
        const retrievalComponent = components[retrievalId];

        // 确保Retrieval组件的query引用Answer组件
        if (!retrievalComponent.obj.params) {
          retrievalComponent.obj.params = {};
        }

        // 🔥 在agent对话中，正确配置Retrieval参数
        // kb_vars（知识库变量）可以为空，让RAGFlow自动处理
        retrievalComponent.obj.params.kb_vars = [];

        // query是查询输入，应该引用Answer组件的输出作为查询内容
        retrievalComponent.obj.params.query = [
          {
            component_id: answerId,
            type: "reference"
          }
        ];

        console.log(`[DSL构建] 配置Retrieval查询输入: query引用${answerId}, kb_vars为空`);

        // 确保其他参数存在
        if (retrievalComponent.obj.params.rerank === undefined) {
          retrievalComponent.obj.params.rerank = false;
        }

        console.log(`[DSL构建] agent对话循环依赖修复完成: ${keywordExtractId} ↔ ${answerId} ↔ ${retrievalId}`);
      }

      console.log(`[DSL构建] agent对话场景优化完成`);
    }
  }

  console.log('[DSL构建] 最终组件结构检查:');
  Object.keys(components).forEach(nodeId => {
    const component = components[nodeId];
    console.log(`[DSL构建] ${nodeId}: inputs=${component.obj.inputs?.length || 0}, upstream=${component.upstream?.length || 0}`);
  });

  // 最终返回前检查KeywordExtract
  Object.keys(components).forEach(nodeId => {
    if (components[nodeId].obj.component_name === 'KeywordExtract') {
      console.log(`[DSL构建] 最终返回前检查 ${nodeId}:`, components[nodeId]);
      console.log(`[DSL构建] 最终返回前检查 ${nodeId} query:`, components[nodeId].obj.params.query);
    }
  });

  return components;
}

/**
 * 建立明确的组件输入输出连接，并处理特殊的工作流模式
 * @param {Object} components - 组件配置对象
 * @param {Object} context - 上下文信息，包含对话历史等
 */
function clearRuntimeData(components) {
  console.log('[DSL构建] 建立明确的组件输入连接');
  console.log('[DSL构建] 处理组件数量:', Object.keys(components).length);

  // 检测是否存在Answer组件，如果存在则需要建立循环依赖
  const answerComponents = Object.keys(components).filter(nodeId =>
    components[nodeId].obj.component_name === 'Answer'
  );

  const keywordComponents = Object.keys(components).filter(nodeId =>
    components[nodeId].obj.component_name === 'KeywordExtract'
  );

  // 如果同时存在Answer和KeywordExtract组件，建立循环依赖
  if (answerComponents.length > 0 && keywordComponents.length > 0) {
    console.log('[DSL构建] 检测到Answer和KeywordExtract组件，建立循环依赖');
    establishCircularDependency(components, answerComponents[0], keywordComponents[0]);
  }

  Object.keys(components).forEach(nodeId => {
    const component = components[nodeId];
    const componentName = component.obj.component_name;

    console.log(`[DSL构建] 处理组件 ${nodeId} (${componentName}), 上游:`, component.upstream);

    // 🔥 不再创建inputs字段，让RAGFlow根据upstream/downstream自动推断
    // RAGFlow会根据组件的upstream连接自动确定输入来源
    console.log(`[DSL构建] ${nodeId} 跳过inputs创建，使用upstream/downstream连接: ${component.upstream?.join(', ') || '无'}`);

    // 🔥 确保没有inputs字段
    if (component.obj.inputs) {
      delete component.obj.inputs;
      console.log(`[DSL构建] 删除 ${nodeId} 的inputs字段，使用upstream连接`);
    }

    // 🔥 不再设置任何组件的output结构，让RAGFlow完全控制
    console.log(`[DSL构建] ${componentName} ${nodeId} 跳过output设置，让RAGFlow动态生成`);

    console.log(`[DSL构建] ${nodeId}(${componentName}) 输入输出连接已建立`);
  });

  // 验证和修复组件输入内容
  validateAndFixComponentInputs(components);
}

/**
 * 从查询文本中提取关键词
 * @param {string} query - 用户查询文本
 * @returns {string} - 逗号分隔的关键词字符串
 */
function extractKeywordsFromQuery(query) {
  if (!query || !query.trim()) {
    return "用户查询, 关键词, 提取";
  }

  const text = query.trim();
  console.log(`[关键词提取] 处理查询: "${text}"`);

  // 预定义的关键词映射
  const keywordMappings = {
    '售前': ['售前', '介绍', '相关事项'],
    '技术': ['技术', '支持', '问题'],
    '产品': ['产品', '功能', '特性'],
    '价格': ['价格', '费用', '成本'],
    '服务': ['服务', '支持', '帮助'],
    '安装': ['安装', '部署', '配置'],
    '使用': ['使用', '操作', '指南'],
    '问题': ['问题', '故障', '解决'],
    '咨询': ['咨询', '询问', '了解'],
    '购买': ['购买', '采购', '订购']
  };

  // 尝试匹配预定义关键词
  for (const [key, keywords] of Object.entries(keywordMappings)) {
    if (text.includes(key)) {
      const result = keywords.join(', ');
      console.log(`[关键词提取] 匹配到 "${key}": ${result}`);
      return result;
    }
  }

  // 如果没有匹配到预定义关键词，进行简单的文本分析
  const words = text.split(/[\s，,。！？；：\n\r]+/).filter(word =>
    word.length > 1 && !['的', '了', '是', '在', '有', '和', '与', '或', '但', '而', '就', '都', '也', '还', '只', '又', '再'].includes(word)
  );

  if (words.length > 0) {
    // 取前3个有意义的词作为关键词
    const extractedWords = words.slice(0, 3);
    const result = extractedWords.join(', ');
    console.log(`[关键词提取] 文本分析结果: ${result}`);
    return result;
  }

  // 如果都没有提取到，返回默认值
  console.log(`[关键词提取] 使用默认关键词`);
  return "用户查询, 关键词, 提取";
}

/**
 * 验证和修复组件输入内容
 * @param {Object} components - 组件配置对象
 */
function validateAndFixComponentInputs(components) {
  console.log('[DSL验证] 开始验证组件输入内容');

  Object.keys(components).forEach(nodeId => {
    const component = components[nodeId];
    const componentName = component.obj.component_name;

    if (component.obj.inputs && component.obj.inputs.length > 0) {
      component.obj.inputs.forEach((input, index) => {
        const originalContent = input.content;
        let isValid = true;
        let fixedContent = originalContent;

        // 检查输入内容是否有效
        if (!originalContent || originalContent.trim() === '') {
          isValid = false;
          console.warn(`[DSL验证] ${nodeId} 输入${index} 内容为空`);
        } else if (originalContent.includes('_') && originalContent.length < 20 && !originalContent.includes(' ')) {
          // 可能是组件ID而不是实际内容
          isValid = false;
          console.warn(`[DSL验证] ${nodeId} 输入${index} 疑似组件ID: ${originalContent}`);
        }

        // 针对特定组件类型进行验证和修复
        if (!isValid) {
          switch (componentName) {
            case 'Categorize':
              // Categorize组件需要关键词或查询内容
              if (input.component_id && input.component_id.includes('KeywordExtract')) {
                fixedContent = "用户查询, 分类, 关键词";
              } else {
                fixedContent = "用户查询内容";
              }
              break;

            case 'Retrieval':
              // Retrieval组件需要实际的查询文本
              fixedContent = "用户查询内容";
              break;

            case 'Generate':
              // Generate组件需要文档内容
              fixedContent = "\nDocument: 示例文档\nRelevant fragments as following:\n---\nID: 0\n检索到的相关文档内容\n---\n";
              break;

            case 'Answer':
              // Answer组件需要生成的内容
              fixedContent = "生成的回答内容";
              break;

            default:
              fixedContent = "组件输入内容";
          }

          input.content = fixedContent;
          console.log(`[DSL验证] ${nodeId} 输入${index} 已修复: ${originalContent} -> ${fixedContent}`);
        }
      });
    }
  });

  console.log('[DSL验证] 组件输入内容验证完成');
}

/**
 * 专门验证Categorize组件的输入格式
 * @param {Object} components - 组件配置对象
 */
function validateCategorizeInputs(components) {
  console.log('[DSL验证] 开始验证Categorize组件输入格式');

  Object.keys(components).forEach(nodeId => {
    const component = components[nodeId];
    if (component.obj.component_name === 'Categorize') {
      console.log(`[DSL验证] 检查Categorize组件: ${nodeId}`);

      // 🔥 初始状态的组件可能没有inputs，这是正常的
      if (!component.obj.inputs || component.obj.inputs.length === 0) {
        console.log(`[DSL验证] Categorize组件 ${nodeId} 没有输入（初始状态正常）`);
        return; // 跳过验证，让RAGFlow处理
      }

      const input = component.obj.inputs[0];
      const originalContent = input.content;

      // 验证输入内容格式
      let isValidFormat = false;

      if (originalContent && originalContent.trim()) {
        // 检查是否为关键词格式（包含逗号分隔）
        if (originalContent.includes(',') || originalContent.includes('，')) {
          isValidFormat = true;
        }
        // 检查是否为对话格式
        else if (originalContent.includes('USER:') && originalContent.includes('ASSISTANT:')) {
          isValidFormat = true;
        }
        // 检查是否为普通查询文本（不是组件ID）
        else if (!originalContent.includes('_') || originalContent.length > 20 || originalContent.includes(' ')) {
          isValidFormat = true;
        }
      }

      if (!isValidFormat) {
        console.warn(`[DSL验证] Categorize组件 ${nodeId} 输入格式无效: "${originalContent}"`);

        // 尝试从上游组件获取正确的内容
        const upstreamId = input.component_id;
        if (upstreamId && components[upstreamId]) {
          const upstreamComponent = components[upstreamId];
          const upstreamOutput = upstreamComponent.obj.output;

          if (upstreamOutput && upstreamOutput.content) {
            const upstreamContent = upstreamOutput.content['0'];
            if (upstreamContent && upstreamContent.trim()) {
              input.content = upstreamContent;
              console.log(`[DSL验证] Categorize组件 ${nodeId} 输入已从上游修复: "${upstreamContent}"`);
              return;
            }
          }
        }

        // 如果无法从上游获取，使用默认格式
        input.content = "用户查询, 分类, 关键词";
        console.log(`[DSL验证] Categorize组件 ${nodeId} 输入已设置为默认格式`);
      } else {
        console.log(`[DSL验证] Categorize组件 ${nodeId} 输入格式有效: "${originalContent}"`);
      }
    }
  });

  console.log('[DSL验证] Categorize组件输入格式验证完成');
}

/**
 * 验证KeywordExtract组件的输出格式
 * @param {Object} components - 组件配置对象
 */
function validateKeywordExtractOutputs(components) {
  console.log('[DSL验证] 开始验证KeywordExtract组件输出格式');

  Object.keys(components).forEach(nodeId => {
    const component = components[nodeId];
    if (component.obj.component_name === 'KeywordExtract') {
      console.log(`[DSL验证] 检查KeywordExtract组件: ${nodeId}`);

      // 🔥 初始状态的组件可能没有output，这是正常的
      if (!component.obj.output) {
        console.log(`[DSL验证] KeywordExtract组件 ${nodeId} 没有输出（初始状态正常）`);
        return; // 跳过验证，让RAGFlow处理
      }

      const output = component.obj.output;
      const outputContent = output.content?.['0'];

      console.log(`[DSL验证] KeywordExtract ${nodeId} 输出内容:`, outputContent);

      // 验证输出内容格式
      let isValidOutput = false;

      if (outputContent && typeof outputContent === 'string' && outputContent.trim()) {
        // 检查是否为关键词格式
        if (outputContent.includes(',') || outputContent.includes('，')) {
          isValidOutput = true;
          console.log(`[DSL验证] KeywordExtract ${nodeId} 输出格式有效（关键词格式）`);
        } else if (outputContent.length > 0 && !outputContent.includes('_')) {
          // 单个关键词也是有效的
          isValidOutput = true;
          console.log(`[DSL验证] KeywordExtract ${nodeId} 输出格式有效（单个关键词）`);
        }
      }

      if (!isValidOutput) {
        console.warn(`[DSL验证] KeywordExtract ${nodeId} 输出格式无效，进行修复`);

        // 🔥 不再设置KeywordExtract的输出内容，让RAGFlow动态生成
        console.log(`[DSL验证] KeywordExtract ${nodeId} 跳过输出修复，让RAGFlow动态生成`);
        output.content['0'] = "";  // 清空预设内容
      }

      // 确保下游Categorize组件能正确接收
      const downstreamComponents = Object.keys(components).filter(id => {
        const comp = components[id];
        return comp.upstream && comp.upstream.includes(nodeId) &&
               comp.obj.component_name === 'Categorize';
      });

      if (downstreamComponents.length > 0) {
        console.log(`[DSL验证] KeywordExtract ${nodeId} 有 ${downstreamComponents.length} 个下游Categorize组件`);
        downstreamComponents.forEach(catId => {
          const catComponent = components[catId];
          if (catComponent.obj.inputs) {
            const keywordInput = catComponent.obj.inputs.find(input => input.component_id === nodeId);
            if (keywordInput) {
              keywordInput.content = output.content['0'];
              console.log(`[DSL验证] 已更新Categorize ${catId} 的输入内容`);
            }
          }
        });
      }
    }
  });

  console.log('[DSL验证] KeywordExtract组件输出格式验证完成');
}

/**
 * 动态输入更新机制 - 核心类
 */
class DynamicInputUpdater {
  constructor(components, context = {}) {
    this.components = components;
    this.context = context;
    this.updateHistory = [];
    this.executionOrder = [];
    this.componentStates = {};

    console.log('[动态更新] 初始化动态输入更新器');
    this.initializeComponentStates();
  }

  /**
   * 初始化组件状态
   */
  initializeComponentStates() {
    Object.keys(this.components).forEach(nodeId => {
      this.componentStates[nodeId] = {
        status: 'pending',  // pending, processing, completed, error
        lastUpdated: null,
        inputsUpdated: 0,
        outputGenerated: false,
        dependencies: this.components[nodeId].upstream || [],
        dependents: this.getDependents(nodeId)
      };
    });

    // 计算执行顺序
    this.calculateExecutionOrder();
  }

  /**
   * 获取组件的依赖者（下游组件）
   */
  getDependents(nodeId) {
    return Object.keys(this.components).filter(id => {
      const component = this.components[id];
      return component.upstream && component.upstream.includes(nodeId);
    });
  }

  /**
   * 计算组件执行顺序（拓扑排序）
   */
  calculateExecutionOrder() {
    const visited = new Set();
    const visiting = new Set();
    const order = [];

    const visit = (nodeId) => {
      if (visiting.has(nodeId)) {
        console.warn(`[动态更新] 检测到循环依赖: ${nodeId}`);
        return;
      }
      if (visited.has(nodeId)) return;

      visiting.add(nodeId);

      const dependencies = this.componentStates[nodeId].dependencies;
      dependencies.forEach(depId => {
        if (this.components[depId]) {
          visit(depId);
        }
      });

      visiting.delete(nodeId);
      visited.add(nodeId);
      order.push(nodeId);
    };

    Object.keys(this.components).forEach(nodeId => {
      if (!visited.has(nodeId)) {
        visit(nodeId);
      }
    });

    this.executionOrder = order;
    console.log('[动态更新] 计算执行顺序:', this.executionOrder);
  }

  /**
   * 执行完整的动态更新流程
   */
  async executeFullUpdate() {
    console.group('[动态更新] 开始执行完整的动态更新流程');

    try {
      // 阶段1: 预处理
      await this.preprocessComponents();

      // 阶段2: 按顺序更新组件
      for (const nodeId of this.executionOrder) {
        await this.updateComponentInputs(nodeId);
      }

      // 阶段3: 后处理验证
      await this.postprocessValidation();

      console.log('[动态更新] 完整更新流程执行完成');
      return true;
    } catch (error) {
      console.error('[动态更新] 更新流程执行失败:', error);
      return false;
    } finally {
      console.groupEnd();
    }
  }

  /**
   * 预处理组件
   */
  async preprocessComponents() {
    console.log('[动态更新] 阶段1: 预处理组件');

    // 处理Begin组件
    const beginComponents = Object.keys(this.components).filter(id =>
      this.components[id].obj.component_name === 'Begin'
    );

    beginComponents.forEach(beginId => {
      this.updateBeginComponent(beginId);
      this.componentStates[beginId].status = 'completed';
      this.componentStates[beginId].outputGenerated = true;
    });

    // 处理特殊的循环依赖
    await this.handleCircularDependencies();
  }

  /**
   * 处理循环依赖
   */
  async handleCircularDependencies() {
    const answerComponents = Object.keys(this.components).filter(id =>
      this.components[id].obj.component_name === 'Answer'
    );

    const keywordComponents = Object.keys(this.components).filter(id =>
      this.components[id].obj.component_name === 'KeywordExtract'
    );

    if (answerComponents.length > 0 && keywordComponents.length > 0) {
      console.log('[动态更新] 处理Answer-KeywordExtract循环依赖');

      // 为KeywordExtract提供初始输入
      keywordComponents.forEach(keywordId => {
        this.updateKeywordExtractInitialInput(keywordId);
      });
    }
  }

  /**
   * 更新单个组件的输入
   */
  async updateComponentInputs(nodeId) {
    const component = this.components[nodeId];
    const componentName = component.obj.component_name;

    console.log(`[动态更新] 更新组件 ${nodeId} (${componentName})`);

    // 检查依赖是否已完成
    const dependencies = this.componentStates[nodeId].dependencies;
    const uncompletedDeps = dependencies.filter(depId =>
      this.componentStates[depId]?.status !== 'completed'
    );

    if (uncompletedDeps.length > 0) {
      console.warn(`[动态更新] ${nodeId} 依赖未完成:`, uncompletedDeps);
      // 先更新依赖
      for (const depId of uncompletedDeps) {
        await this.updateComponentInputs(depId);
      }
    }

    this.componentStates[nodeId].status = 'processing';

    try {
      // 根据组件类型执行特定的更新逻辑
      switch (componentName) {
        case 'KeywordExtract':
          await this.updateKeywordExtractComponent(nodeId);
          break;
        case 'Categorize':
          await this.updateCategorizeComponent(nodeId);
          break;
        case 'Retrieval':
          await this.updateRetrievalComponent(nodeId);
          break;
        case 'Generate':
          await this.updateGenerateComponent(nodeId);
          break;
        case 'Answer':
          await this.updateAnswerComponent(nodeId);
          break;
        default:
          await this.updateGenericComponent(nodeId);
      }

      this.componentStates[nodeId].status = 'completed';
      this.componentStates[nodeId].lastUpdated = Date.now();
      this.componentStates[nodeId].outputGenerated = true;

      // 通知下游组件
      await this.notifyDependents(nodeId);

    } catch (error) {
      console.error(`[动态更新] ${nodeId} 更新失败:`, error);
      this.componentStates[nodeId].status = 'error';
    }
  }

  /**
   * 更新KeywordExtract组件
   */
  async updateKeywordExtractComponent(nodeId) {
    console.log(`[动态更新] KeywordExtract ${nodeId} 跳过更新，让RAGFlow动态处理`);

    // 🔥 不再更新KeywordExtract的inputs和output
    // 让RAGFlow根据upstream连接自动处理

    this.recordUpdate(nodeId, 'KeywordExtract', '由RAGFlow处理', '由RAGFlow生成');
  }

  /**
   * 更新Categorize组件
   */
  async updateCategorizeComponent(nodeId) {
    console.log(`[动态更新] Categorize ${nodeId} 跳过更新，让RAGFlow动态处理`);

    // 🔥 不再更新Categorize的inputs和output
    // 让RAGFlow根据upstream连接自动处理

    this.recordUpdate(nodeId, 'Categorize', '由RAGFlow处理', '由RAGFlow生成');
  }

  /**
   * 更新Retrieval组件
   */
  async updateRetrievalComponent(nodeId) {
    console.log(`[动态更新] Retrieval ${nodeId} 跳过更新，让RAGFlow动态处理`);

    // 🔥 不再更新Retrieval的inputs和output
    // 让RAGFlow根据upstream连接自动处理

    this.recordUpdate(nodeId, 'Retrieval', '由RAGFlow处理', '由RAGFlow生成');
  }

  /**
   * 更新Generate组件
   */
  async updateGenerateComponent(nodeId) {
    console.log(`[动态更新] Generate ${nodeId} 跳过更新，让RAGFlow动态处理`);

    // 🔥 不再更新Generate的inputs和output
    // 让RAGFlow根据upstream连接自动处理

    this.recordUpdate(nodeId, 'Generate', '由RAGFlow处理', '由RAGFlow生成');
  }

  /**
   * 更新Answer组件
   */
  async updateAnswerComponent(nodeId) {
    console.log(`[动态更新] Answer ${nodeId} 跳过更新，让RAGFlow动态处理`);

    // 🔥 不再更新Answer的inputs和output
    // 让RAGFlow根据upstream连接自动处理

    this.recordUpdate(nodeId, 'Answer', '由RAGFlow处理', '由RAGFlow生成');
  }

  /**
   * 更新通用组件
   */
  async updateGenericComponent(nodeId) {
    const component = this.components[nodeId];
    const componentName = component.obj.component_name;

    console.log(`[动态更新] ${componentName} ${nodeId} 跳过更新，让RAGFlow动态处理`);

    // 🔥 不再更新通用组件的inputs和output
    // 让RAGFlow根据upstream连接自动处理

    this.recordUpdate(nodeId, componentName, '由RAGFlow处理', '由RAGFlow生成');
  }

  /**
   * 更新Begin组件
   */
  updateBeginComponent(nodeId) {
    const component = this.components[nodeId];

    // Begin组件通常有固定的输出
    const prologue = component.obj.params?.prologue || "Hi! I'm your smart assistant. What can I do for you?";

    component.obj.output = {
      content: {
        "0": {
          content: prologue,
          role: "assistant"
        }
      }
    };

    console.log(`[动态更新] Begin ${nodeId} 已初始化`);
  }

  /**
   * 更新KeywordExtract初始输入
   */
  updateKeywordExtractInitialInput(nodeId) {
    const component = this.components[nodeId];

    // 构造初始对话历史
    const userQuery = this.context.userQuery || "你帮我介绍一下售前的相关事项";
    const conversationHistory = `ASSISTANT:Hi! I'm your smart assistant. What can I do for you?\nUSER:${userQuery}`;

    component.obj.inputs = [{
      component_id: 'begin',
      content: conversationHistory
    }];

    console.log(`[动态更新] KeywordExtract ${nodeId} 初始输入已设置`);
  }

  /**
   * 获取最新的输入内容
   */
  getLatestInputContent(nodeId) {
    const component = this.components[nodeId];
    const componentName = component.obj.component_name;

    if (componentName === 'KeywordExtract') {
      // 优先从context获取对话历史
      if (this.context.conversationHistory) {
        return this.context.conversationHistory;
      }

      // 从上游Answer组件获取
      const answerOutput = this.getUpstreamOutput(nodeId, 'Answer');
      if (answerOutput) {
        return answerOutput;
      }

      // 构造默认对话历史
      const userQuery = this.context.userQuery || "你帮我介绍一下售前的相关事项";
      return `ASSISTANT:Hi! I'm your smart assistant. What can I do for you?\nUSER:${userQuery}`;
    }

    // 其他组件从上游获取
    return this.getUpstreamOutput(nodeId) || "默认输入内容";
  }

  /**
   * 获取上游组件的输出
   */
  getUpstreamOutput(nodeId, upstreamType = null, specificUpstreamId = null) {
    const component = this.components[nodeId];

    if (!component.upstream || component.upstream.length === 0) {
      return null;
    }

    // 如果指定了特定的上游ID
    if (specificUpstreamId && this.components[specificUpstreamId]) {
      const upstreamComponent = this.components[specificUpstreamId];
      return this.extractOutputContent(upstreamComponent);
    }

    // 如果指定了上游类型
    if (upstreamType) {
      const upstreamId = component.upstream.find(id =>
        this.components[id]?.obj.component_name === upstreamType
      );

      if (upstreamId && this.components[upstreamId]) {
        const upstreamComponent = this.components[upstreamId];
        return this.extractOutputContent(upstreamComponent);
      }
    }

    // 默认获取第一个上游组件的输出
    const firstUpstreamId = component.upstream[0];
    if (this.components[firstUpstreamId]) {
      const upstreamComponent = this.components[firstUpstreamId];
      return this.extractOutputContent(upstreamComponent);
    }

    return null;
  }

  /**
   * 提取组件输出内容
   */
  extractOutputContent(component) {
    if (!component.obj.output) {
      return null;
    }

    const output = component.obj.output;

    // 处理不同的输出格式
    if (output.content) {
      const content = output.content['0'];

      if (typeof content === 'string') {
        return content;
      } else if (content && content.content) {
        return content.content;
      } else if (content) {
        return JSON.stringify(content);
      }
    }

    return null;
  }

  /**
   * 通知依赖组件
   */
  async notifyDependents(nodeId) {
    const dependents = this.componentStates[nodeId].dependents;

    for (const dependentId of dependents) {
      if (this.componentStates[dependentId].status === 'pending') {
        console.log(`[动态更新] 通知下游组件 ${dependentId}`);
        // 这里可以触发下游组件的更新
        // 但要避免无限循环
      }
    }
  }

  /**
   * 构建对话历史
   */
  buildConversationHistory() {
    const beginComponent = Object.values(this.components).find(comp =>
      comp.obj.component_name === 'Begin'
    );

    const beginContent = beginComponent?.obj?.params?.prologue ||
                        "Hi! I'm your smart assistant. What can I do for you?";

    const userQuery = this.context.userQuery || "你帮我介绍一下售前的相关事项";

    return `ASSISTANT:${beginContent}\nUSER:${userQuery}`;
  }

  /**
   * 记录更新历史
   */
  recordUpdate(nodeId, componentType, input, output) {
    const updateRecord = {
      timestamp: Date.now(),
      nodeId,
      componentType,
      input: input ? input.substring(0, 100) + (input.length > 100 ? '...' : '') : '',
      output: output ? output.substring(0, 100) + (output.length > 100 ? '...' : '') : '',
      inputLength: input ? input.length : 0,
      outputLength: output ? output.length : 0
    };

    this.updateHistory.push(updateRecord);
    this.componentStates[nodeId].inputsUpdated++;

    console.log(`[动态更新] 记录更新: ${nodeId} (${componentType})`);
  }

  /**
   * 后处理验证
   */
  async postprocessValidation() {
    console.log('[动态更新] 阶段3: 后处理验证');

    // 验证所有组件都有有效输入
    const invalidComponents = [];

    Object.keys(this.components).forEach(nodeId => {
      const component = this.components[nodeId];
      const componentName = component.obj.component_name;

      if (componentName !== 'Begin' && (!component.obj.inputs || component.obj.inputs.length === 0)) {
        invalidComponents.push(nodeId);
      }
    });

    if (invalidComponents.length > 0) {
      console.warn('[动态更新] 发现无效组件:', invalidComponents);

      // 尝试修复
      for (const nodeId of invalidComponents) {
        await this.updateComponentInputs(nodeId);
      }
    }

    // 验证关键路径
    this.validateCriticalPath();
  }

  /**
   * 验证关键路径
   */
  validateCriticalPath() {
    console.log('[动态更新] 验证关键路径');

    // 检查KeywordExtract -> Categorize -> Retrieval -> Generate -> Answer路径
    const criticalComponents = ['KeywordExtract', 'Categorize', 'Retrieval', 'Generate', 'Answer'];

    criticalComponents.forEach(componentType => {
      const components = Object.keys(this.components).filter(id =>
        this.components[id].obj.component_name === componentType
      );

      components.forEach(nodeId => {
        const state = this.componentStates[nodeId];
        if (state.status !== 'completed') {
          console.warn(`[动态更新] 关键组件 ${nodeId} (${componentType}) 状态异常: ${state.status}`);
        }
      });
    });
  }

  /**
   * 获取更新报告
   */
  getUpdateReport() {
    return {
      totalComponents: Object.keys(this.components).length,
      completedComponents: Object.values(this.componentStates).filter(state => state.status === 'completed').length,
      errorComponents: Object.values(this.componentStates).filter(state => state.status === 'error').length,
      updateHistory: this.updateHistory,
      executionOrder: this.executionOrder,
      componentStates: this.componentStates
    };
  }
}

/**
 * 安全的动态输入更新（防止死循环）
 * @param {Object} components - 组件配置对象
 * @param {Object} context - 上下文信息
 */
async function executeSafeDynamicInputUpdate(components, context = {}) {
  console.group('[安全动态更新] 执行安全的动态输入更新');

  // 防死循环保护
  const MAX_UPDATE_ITERATIONS = 3;
  const MAX_COMPONENT_UPDATES = 10;
  let iterationCount = 0;
  const componentUpdateCounts = {};

  try {
    console.log('[安全动态更新] 开始安全更新，最大迭代次数:', MAX_UPDATE_ITERATIONS);

    while (iterationCount < MAX_UPDATE_ITERATIONS) {
      iterationCount++;
      console.log(`[安全动态更新] 第 ${iterationCount} 次迭代`);

      let hasUpdates = false;

      // 按组件类型顺序更新，避免循环依赖
      const updateOrder = ['Begin', 'KeywordExtract', 'Categorize', 'Retrieval', 'Generate', 'Answer'];

      for (const componentType of updateOrder) {
        const componentsOfType = Object.keys(components).filter(id =>
          components[id].obj.component_name === componentType
        );

        for (const nodeId of componentsOfType) {
          // 检查更新次数限制
          if (!componentUpdateCounts[nodeId]) {
            componentUpdateCounts[nodeId] = 0;
          }

          if (componentUpdateCounts[nodeId] >= MAX_COMPONENT_UPDATES) {
            console.warn(`[安全动态更新] ${nodeId} 已达到最大更新次数，跳过`);
            continue;
          }

          const updated = await updateSingleComponentSafely(components, nodeId, context);
          if (updated) {
            hasUpdates = true;
            componentUpdateCounts[nodeId]++;
            console.log(`[安全动态更新] ${nodeId} 更新成功 (第${componentUpdateCounts[nodeId]}次)`);
          }
        }
      }

      // 如果没有更新，说明已经稳定
      if (!hasUpdates) {
        console.log('[安全动态更新] 没有更新，系统已稳定');
        break;
      }
    }

    if (iterationCount >= MAX_UPDATE_ITERATIONS) {
      console.warn('[安全动态更新] 达到最大迭代次数，强制停止');
    }

    // 最终验证
    const issues = validateInputOutputChain(components);

    console.log(`[安全动态更新] 完成，迭代次数: ${iterationCount}, 问题数: ${issues.length}`);

    return {
      success: issues.length === 0,
      iterations: iterationCount,
      issues: issues,
      componentUpdateCounts: componentUpdateCounts
    };

  } catch (error) {
    console.error('[安全动态更新] 执行异常:', error);
    return { success: false, error, iterations: iterationCount };
  } finally {
    console.groupEnd();
  }
}

/**
 * 安全地更新单个组件
 * @param {Object} components - 组件配置对象
 * @param {string} nodeId - 组件ID
 * @param {Object} context - 上下文信息
 */
async function updateSingleComponentSafely(components, nodeId, context = {}) {
  const component = components[nodeId];
  if (!component) return false;

  const componentName = component.obj.component_name;
  console.log(`[安全更新] 更新组件 ${nodeId} (${componentName})`);

  try {
    let updated = false;

    switch (componentName) {
      case 'Begin':
        // Begin组件不需要更新输入
        break;

      case 'KeywordExtract': {
        const newInput = getKeywordExtractInput(components, nodeId, context);
        if (updateComponentInput(component, newInput)) {
          updateKeywordExtractOutput(component, newInput);
          updated = true;
        }
        break;
      }

      case 'Categorize': {
        const newInput = getCategorizeInput(components, nodeId, context);
        if (updateComponentInput(component, newInput)) {
          updated = true;
        }
        break;
      }

      case 'Retrieval': {
        const newInput = getRetrievalInput(components, nodeId, context);
        if (updateComponentInput(component, newInput)) {
          updated = true;
        }
        break;
      }

      case 'Generate': {
        const newInput = getGenerateInput(components, nodeId, context);
        if (updateComponentInput(component, newInput)) {
          updated = true;
        }
        break;
      }

      case 'Answer': {
        const newInput = getAnswerInput(components, nodeId, context);
        if (updateComponentInput(component, newInput)) {
          updateAnswerOutput(component, context);
          updated = true;
        }
        break;
      }
    }

    return updated;

  } catch (error) {
    console.error(`[安全更新] ${nodeId} 更新失败:`, error);
    return false;
  }
}

/**
 * 更新组件输入内容
 * @param {Object} component - 组件对象
 * @param {string} newInput - 新的输入内容
 */
function updateComponentInput(component, newInput) {
  if (!newInput || newInput.trim() === '') return false;

  const currentInput = component.obj.inputs?.[0]?.content || '';

  // 如果内容没有变化，不需要更新
  if (currentInput === newInput) return false;

  // 更新输入
  if (!component.obj.inputs) {
    component.obj.inputs = [];
  }

  if (component.obj.inputs.length === 0) {
    component.obj.inputs.push({
      component_id: component.upstream?.[0] || 'begin',
      content: newInput
    });
  } else {
    component.obj.inputs[0].content = newInput;
  }

  console.log(`[安全更新] 输入已更新: "${currentInput}" -> "${newInput}"`);
  return true;
}

/**
 * 获取KeywordExtract组件的输入
 */
function getKeywordExtractInput(/* components, nodeId, */ context) {
  // 优先使用context中的对话历史
  if (context.conversationHistory && context.conversationHistory.trim()) {
    return context.conversationHistory;
  }

  // 构造默认对话历史
  const userQuery = context.userQuery || "你帮我介绍一下售前的相关事项";
  return `ASSISTANT:Hi! I'm your smart assistant. What can I do for you?\nUSER:${userQuery}`;
}

/**
 * 获取Categorize组件的输入
 */
function getCategorizeInput(components, nodeId, context) {
  const component = components[nodeId];
  if (!component.upstream || component.upstream.length === 0) return '';

  // 从上游KeywordExtract获取关键词
  const upstreamId = component.upstream[0];
  const upstreamComponent = components[upstreamId];

  if (upstreamComponent && upstreamComponent.obj.component_name === 'KeywordExtract') {
    const output = upstreamComponent.obj.output?.content?.['0'];
    if (output && output.trim()) {
      return output;
    }
  }

  // 使用context中的关键词
  return context.keywords || "用户查询, 分类, 关键词";
}

/**
 * 获取Retrieval组件的输入
 */
function getRetrievalInput(components, nodeId, context) {
  const component = components[nodeId];
  if (!component.upstream || component.upstream.length === 0) return '';

  // 从上游Categorize获取查询内容
  const upstreamId = component.upstream[0];
  const upstreamComponent = components[upstreamId];

  if (upstreamComponent && upstreamComponent.obj.component_name === 'Categorize') {
    const input = upstreamComponent.obj.inputs?.[0]?.content;
    if (input && input.trim()) {
      return input;
    }
  }

  return context.userQuery || "用户查询内容";
}

/**
 * 获取Generate组件的输入
 */
function getGenerateInput(/* components, nodeId, context */) {
  return "\nDocument: 示例文档\nRelevant fragments as following:\n---\nID: 0\n检索到的相关文档内容\n---\n";
}

/**
 * 获取Answer组件的输入
 */
function getAnswerInput(/* components, nodeId, context */) {
  return "生成的回答内容";
}

/**
 * 更新KeywordExtract组件的输出
 */
function updateKeywordExtractOutput(/* component, inputContent */) {
  // 🔥 不再更新KeywordExtract的输出，让RAGFlow动态生成
  console.log('[DSL构建] KeywordExtract输出更新已跳过，让RAGFlow处理');
}

/**
 * 更新Answer组件的输出
 */
function updateAnswerOutput(component, context) {
  const userQuery = context.userQuery || "你帮我介绍一下售前的相关事项";
  const conversationHistory = `ASSISTANT:Hi! I'm your smart assistant. What can I do for you?\nUSER:${userQuery}`;

  component.obj.output = {
    content: { "0": conversationHistory }
  };
}

/**
 * 实时更新组件输入（增量更新）
 * @param {Object} components - 组件配置对象
 * @param {string} changedNodeId - 发生变化的组件ID
 * @param {Object} context - 上下文信息
 */
async function updateComponentInputsRealtime(components, changedNodeId, context = {}) {
  console.log(`[动态更新] 实时更新: ${changedNodeId} 发生变化`);

  const updater = new DynamicInputUpdater(components, context);

  // 找到所有受影响的下游组件
  const affectedComponents = updater.getDependents(changedNodeId);

  console.log(`[动态更新] 受影响的下游组件:`, affectedComponents);

  // 递归更新所有受影响的组件
  const updateQueue = [...affectedComponents];
  const updated = new Set();

  while (updateQueue.length > 0) {
    const nodeId = updateQueue.shift();

    if (updated.has(nodeId)) continue;

    try {
      await updater.updateComponentInputs(nodeId);
      updated.add(nodeId);

      // 添加下游组件到更新队列
      const dependents = updater.getDependents(nodeId);
      dependents.forEach(depId => {
        if (!updated.has(depId) && !updateQueue.includes(depId)) {
          updateQueue.push(depId);
        }
      });

    } catch (error) {
      console.error(`[动态更新] 更新 ${nodeId} 失败:`, error);
    }
  }

  console.log(`[动态更新] 实时更新完成，共更新 ${updated.size} 个组件`);
  return Array.from(updated);
}









/**
 * 验证组件输入输出链的完整性
 * @param {Object} components - 组件配置对象
 */
function validateInputOutputChain(components) {
  console.group('[动态更新] 验证输入输出链完整性');

  const issues = [];

  Object.keys(components).forEach(nodeId => {
    const component = components[nodeId];
    const componentName = component.obj.component_name;

    // 检查输入
    if (componentName !== 'Begin') {
      if (!component.obj.inputs || component.obj.inputs.length === 0) {
        issues.push({
          type: 'missing_input',
          nodeId,
          componentName,
          message: '缺少输入连接'
        });
      } else {
        component.obj.inputs.forEach((input, index) => {
          if (!input.content || input.content.trim() === '') {
            issues.push({
              type: 'empty_input',
              nodeId,
              componentName,
              inputIndex: index,
              message: `输入${index}内容为空`
            });
          }

          if (input.content && input.content.includes('_') && input.content.length < 20 && !input.content.includes(' ')) {
            issues.push({
              type: 'invalid_input_format',
              nodeId,
              componentName,
              inputIndex: index,
              content: input.content,
              message: `输入${index}疑似组件ID格式`
            });
          }
        });
      }
    }

    // 检查输出
    if (!component.obj.output) {
      issues.push({
        type: 'missing_output',
        nodeId,
        componentName,
        message: '缺少输出结构'
      });
    }

    // 检查上游连接
    if (component.upstream && component.upstream.length > 0) {
      component.upstream.forEach(upstreamId => {
        if (!components[upstreamId]) {
          issues.push({
            type: 'broken_upstream',
            nodeId,
            componentName,
            upstreamId,
            message: `上游组件${upstreamId}不存在`
          });
        }
      });
    }
  });

  if (issues.length === 0) {
    console.log('[动态更新] ✅ 输入输出链完整性验证通过');
  } else {
    console.warn(`[动态更新] ⚠️ 发现 ${issues.length} 个问题:`);
    issues.forEach(issue => {
      console.warn(`  ${issue.type}: ${issue.nodeId} - ${issue.message}`);
    });
  }

  console.groupEnd();
  return issues;
}

/**
 * 建立Answer和KeywordExtract之间的循环依赖关系
 * @param {Object} components - 组件配置对象
 * @param {string} answerNodeId - Answer组件ID
 * @param {string} keywordNodeId - KeywordExtract组件ID
 */
function establishCircularDependency(components, answerNodeId, keywordNodeId) {
  console.log(`[DSL构建] 建立循环依赖: ${answerNodeId} <-> ${keywordNodeId}`);

  const answerComponent = components[answerNodeId];
  const keywordComponent = components[keywordNodeId];

  // Answer组件添加到KeywordExtract的下游
  if (!answerComponent.downstream.includes(keywordNodeId)) {
    answerComponent.downstream.push(keywordNodeId);
  }

  // KeywordExtract组件添加Answer为上游
  if (!keywordComponent.upstream.includes(answerNodeId)) {
    keywordComponent.upstream.push(answerNodeId);
  }

  // 更新KeywordExtract的query参数，引用Answer组件
  if (!keywordComponent.obj.params.query) {
    keywordComponent.obj.params.query = [];
  }

  // 检查是否已经有Answer的引用
  const hasAnswerRef = keywordComponent.obj.params.query.some(q =>
    q.component_id === answerNodeId
  );

  if (!hasAnswerRef) {
    keywordComponent.obj.params.query.push({
      component_id: answerNodeId,
      type: "reference"
    });
  }

  console.log(`[DSL构建] 循环依赖建立完成: ${answerNodeId} -> ${keywordNodeId} -> ... -> ${answerNodeId}`);
}

/**
 * 检测并修复循环依赖
 * @param {Object} components - 组件配置对象
 */
function fixCircularDependencies(components) {
  console.log('[DSL构建] 开始检测和修复循环依赖');

  // 检测循环依赖
  const cycles = detectCycles(components);
  if (cycles.length > 0) {
    console.warn('[DSL构建] 检测到循环依赖:', cycles);

    // 修复循环依赖
    cycles.forEach(cycle => {
      fixCycle(components, cycle);
    });
  }

  // 确保Answer组件在工作流末端
  ensureAnswerAtEnd(components);
}

/**
 * 检测循环依赖
 * @param {Object} components - 组件配置对象
 * @returns {Array} 循环依赖数组
 */
function detectCycles(components) {
  const visited = new Set();
  const recursionStack = new Set();
  const cycles = [];

  function dfs(nodeId, path) {
    if (recursionStack.has(nodeId)) {
      // 找到循环
      const cycleStart = path.indexOf(nodeId);
      const cycle = path.slice(cycleStart).concat([nodeId]);
      cycles.push(cycle);
      return;
    }

    if (visited.has(nodeId)) {
      return;
    }

    visited.add(nodeId);
    recursionStack.add(nodeId);
    path.push(nodeId);

    const component = components[nodeId];
    if (component && component.downstream) {
      component.downstream.forEach(downstreamId => {
        if (components[downstreamId]) {
          dfs(downstreamId, [...path]);
        }
      });
    }

    recursionStack.delete(nodeId);
    path.pop();
  }

  Object.keys(components).forEach(nodeId => {
    if (!visited.has(nodeId)) {
      dfs(nodeId, []);
    }
  });

  return cycles;
}

/**
 * 修复单个循环
 * @param {Object} components - 组件配置对象
 * @param {Array} cycle - 循环路径
 */
function fixCycle(components, cycle) {
  console.log('[DSL构建] 修复循环依赖:', cycle);

  // 查找Answer组件在循环中的位置
  const answerIndex = cycle.findIndex(nodeId =>
    components[nodeId]?.obj?.component_name === 'Answer'
  );

  if (answerIndex !== -1) {
    const answerNodeId = cycle[answerIndex];
    const answerComponent = components[answerNodeId];

    // 如果Answer组件不在循环末端，需要重新组织
    if (answerIndex < cycle.length - 2) { // -2 because last element is duplicate of first
      console.log('[DSL构建] Answer组件不在末端，重新组织工作流');

      // 移除Answer组件的下游连接到KeywordExtract的部分
      const keywordExtractNodes = answerComponent.downstream.filter(nodeId =>
        components[nodeId]?.obj?.component_name === 'KeywordExtract'
      );

      keywordExtractNodes.forEach(keywordNodeId => {
        // 将KeywordExtract的上游改为begin
        const keywordComponent = components[keywordNodeId];
        keywordComponent.upstream = keywordComponent.upstream.filter(id => id !== answerNodeId);
        if (!keywordComponent.upstream.includes('begin')) {
          keywordComponent.upstream.push('begin');
        }

        // 将begin的下游改为KeywordExtract
        const beginComponent = components['begin'];
        if (beginComponent) {
          beginComponent.downstream = beginComponent.downstream.filter(id => id !== answerNodeId);
          if (!beginComponent.downstream.includes(keywordNodeId)) {
            beginComponent.downstream.push(keywordNodeId);
          }
        }

        // 移除Answer到KeywordExtract的连接
        answerComponent.downstream = answerComponent.downstream.filter(id => id !== keywordNodeId);
        answerComponent.upstream = answerComponent.upstream.filter(id => id !== 'begin');
      });

      console.log('[DSL构建] 循环依赖修复完成');
    }
  }
}

/**
 * 确保Answer组件在工作流末端
 * @param {Object} components - 组件配置对象
 */
function ensureAnswerAtEnd(components) {
  Object.keys(components).forEach(nodeId => {
    const component = components[nodeId];
    if (component.obj.component_name === 'Answer') {
      // Answer组件不应该有下游（除非是特殊情况）
      const nonAnswerDownstream = component.downstream.filter(downstreamId =>
        components[downstreamId]?.obj?.component_name !== 'Answer'
      );

      if (nonAnswerDownstream.length > 0) {
        console.log(`[DSL构建] Answer组件 ${nodeId} 有非Answer下游，可能需要调整:`, nonAnswerDownstream);

        // 检查下游是否是KeywordExtract（这通常表示循环依赖）
        const keywordDownstream = nonAnswerDownstream.filter(downstreamId =>
          components[downstreamId]?.obj?.component_name === 'KeywordExtract'
        );

        if (keywordDownstream.length > 0) {
          console.log(`[DSL构建] 移除Answer到KeywordExtract的连接:`, keywordDownstream);
          // 这种连接通常是错误的，应该移除
          keywordDownstream.forEach(keywordId => {
            component.downstream = component.downstream.filter(id => id !== keywordId);
            components[keywordId].upstream = components[keywordId].upstream.filter(id => id !== nodeId);
          });
        }
      }
    }
  });
}





// 更新Categorize组件的路由配置
function updateCategorizeComponentsRouting(components, graphData, nodeIdMap) {
  Object.keys(components).forEach(componentId => {
    const component = components[componentId];

    if (component.obj.component_name === 'Categorize') {
      const categoryDesc = component.obj.params.category_description || {};



      // 🔥 修复：根据边的端口信息正确映射分类
      const categoryToTargetMap = new Map();

      // 遍历所有边，找到从这个Categorize组件出发的连接
      graphData.edges.forEach(edge => {
        const originalSource = edge.source?.cell || edge.source;
        const originalTarget = edge.target?.cell || edge.target;

        const newSource = nodeIdMap[originalSource];
        const newTarget = nodeIdMap[originalTarget];

        // 如果这条边是从当前Categorize组件出发的
        if (newSource === componentId && newTarget) {
          // 🔥 从边的sourceHandle或端口信息获取分类索引
          let categoryIndex = null;

          // 方法1: 从sourceHandle获取（如果已经转换为分类名称）
          if (edge.sourceHandle && /[\u4e00-\u9fa5]/.test(edge.sourceHandle)) {
            // sourceHandle是中文分类名称
            // 🔥 方案1：使用原始图表节点ID而不是映射后的DSL组件ID
            categoryToTargetMap.set(edge.sourceHandle, originalTarget);
            return;
          }

          // 方法2: 从源端口获取索引
          const sourcePort = edge.source?.port;
          if (sourcePort && sourcePort.startsWith('category-port-')) {
            const portMatch = sourcePort.match(/category-port-(\d+)/);
            if (portMatch) {
              categoryIndex = parseInt(portMatch[1]);
            }
          }

          // 方法3: 从sourceHandle获取索引（如果是字母格式）
          if (categoryIndex === null && edge.sourceHandle && /^[c-z]$/.test(edge.sourceHandle)) {
            categoryIndex = edge.sourceHandle.charCodeAt(0) - 99; // c=0, d=1, e=2...
          }

          // 根据索引找到对应的分类名称
          if (categoryIndex !== null) {
            Object.keys(categoryDesc).forEach(catName => {
              if (categoryDesc[catName].index === categoryIndex) {
                // 🔥 方案1：使用原始图表节点ID而不是映射后的DSL组件ID
                categoryToTargetMap.set(catName, originalTarget);
              }
            });
          }
        }
      });

      // 🔥 方案3：只更新to字段，保持index不变（index在分类创建时已经正确设置）
      categoryToTargetMap.forEach((target, categoryName) => {
        if (categoryDesc[categoryName]) {
          categoryDesc[categoryName].to = target;
        }
      });

      // 🔥 方案C：同时更新对应图表节点的category_description，确保使用图表节点ID
      console.log(`[方案C调试] 开始更新图表节点，componentId: ${componentId}`);
      console.log(`[方案C调试] categoryToTargetMap:`, categoryToTargetMap);

      const graphNodes = graphData.nodes || [];
      console.log(`[方案C调试] 图表节点数量: ${graphNodes.length}`);

      const categorizeGraphNode = graphNodes.find(node => {
        const mappedId = nodeIdMap[node.id];
        console.log(`[方案C调试] 检查节点: ${node.id} → ${mappedId}, 目标: ${componentId}`);
        return mappedId === componentId;
      });

      console.log(`[方案C调试] 找到的图表节点:`, categorizeGraphNode?.id);

      if (categorizeGraphNode && categorizeGraphNode.data && categorizeGraphNode.data.form) {
        console.log(`[方案C调试] 当前图表节点的category_description:`, categorizeGraphNode.data.form.category_description);

        // 更新图表节点的category_description，使用图表节点ID
        if (!categorizeGraphNode.data.form.category_description) {
          categorizeGraphNode.data.form.category_description = {};
        }

        // 🔥 使用更直接的方式重建整个category_description对象
        const newCategoryDescription = {};
        categoryToTargetMap.forEach((originalTarget, categoryName) => {
          console.log(`[方案C调试] 处理分类: ${categoryName} → ${originalTarget}`);

          // 保持原有的index，使用新的to值
          const originalIndex = categorizeGraphNode.data.form.category_description[categoryName]?.index || 0;
          newCategoryDescription[categoryName] = {
            index: originalIndex,
            to: originalTarget  // 🔥 使用图表节点ID
          };
          console.log(`[方案C调试] 创建新分类: ${categoryName} = {index: ${originalIndex}, to: ${originalTarget}}`);
        });

        // 🔥 直接替换整个category_description对象
        categorizeGraphNode.data.form.category_description = newCategoryDescription;
        console.log(`[方案C调试] 替换后的category_description:`, categorizeGraphNode.data.form.category_description);

        // 🔥 额外验证：再次读取确认修改是否生效
        setTimeout(() => {
          console.log(`[方案C验证] 1秒后再次检查category_description:`, categorizeGraphNode.data.form.category_description);
        }, 1000);

        console.log(`[方案C调试] 最终图表节点的category_description:`, categorizeGraphNode.data.form.category_description);
      } else {
        console.warn(`[方案C调试] 未找到图表节点或节点数据不完整`);
      }
    }
  });
}

export default {
  name: 'App',
  components: {
    X6Graph,
    NavHeader,
    ModelSelector,
    GlobalTooltip,
    CommentDialog,
    LoopDialog,
    IterationItemDialog,
    ClassificationDialog,
    VariableDialog,
    TemplateDialog,
    NodeDrawer,
    AgentManagement,
    ChatDrawer
  },
  data() {
    return {
      currentPage: 'workflow', // 当前页面：workflow, agent, models, datasource
      commentDialogVisible: false,
      currentComment: {
        id: '',
        title: '',
        content: '',
        onSave: null
      },
      loopDialogVisible: false,
      currentLoop: {
        id: '',
        title: '',
        content: '',
        onSave: null
      },
      iterationItemDialogVisible: false,
      currentIterationItem: {
        id: '',
        title: '',
        settings: {
          variable: '',
          source: 'text_segments'
        },
        onSave: null
      },
      classificationDialogVisible: false,
      currentClassification: {
        id: '',
        title: '',
        categories: [],
        onSave: null
      },
      templateDialogVisible: false,
      currentTemplate: {
        id: '',
        title: '',
        description: '',
        content: '',
        onSave: null
      },
      sidebarCollapsed: false, // 侧边栏折叠状态
      globalTooltip: {
        visible: false,
        style: {
          top: '0px',
          left: '0px'
        },
        title: '',
        content: ''
      },
      selectedNode: null, // 当前选中的节点
      nodeDrawerVisible: false, // 节点抽屉是否可见
      addVariableDialogVisible: false,
      chatDrawerVisible: false, // AI对话抽屉是否可见
      currentAgentId: '', // 当前编辑的工作流ID
      currentAgentName: '', // 当前编辑的工作流名称
      currentWorkflowDSL: {}, // 当前工作流的DSL数据
      isSaving: false // 保存状态标识
    };
  },
  methods: {

    // 处理菜单选择
    handleMenuSelect(page) {
      console.log('切换到页面:', page);
      this.currentPage = page;

      // 如果切换到工作流页面，确保图表重新调整大小
      if (page === 'workflow') {
        this.$nextTick(() => {
          if (this.$refs.x6Graph) {
            this.$refs.x6Graph.resizeGraph();
          }
        });
      }
    },
    // 处理Agent编辑跳转到工作流
    handleEditAgent(agent) {
      console.log('[handleEditAgent] 编辑Agent:', agent);
      console.log('[handleEditAgent] Agent DSL:', agent.dsl);
      console.log('[handleEditAgent] Begin组件数据:', agent.dsl?.components?.begin);

      this.currentPage = 'workflow';
      this.currentAgentId = agent.id;
      this.currentAgentName = agent.name || agent.title || '';
      this.$nextTick(() => {
        if (this.$refs.x6Graph) {
          this.$refs.x6Graph.loadWorkflowFromDSL(agent.dsl);
        }
      });
    },

    onModelDragStart(model) {
      console.log('开始拖动模型:', model);
    },

    // 执行工作流初始化，确保数据完整性
    async executeWorkflowInitialization(dsl = null) {
      try {
        console.log('[工作流初始化] 开始执行工作流初始化');

        const targetDSL = dsl || this.currentWorkflowDSL;

        // 1. 初始化Begin组件
        this.initializeBeginComponent(targetDSL);

        // 2. 预填充Retrieval组件的基础结构
        this.initializeRetrievalComponents(targetDSL);

        // 3. 初始化关键词提取组件
        await this.initializeKeywordExtractComponents(targetDSL);

        // 4. 验证组件间的连接完整性
        this.validateComponentConnections(targetDSL);

        console.log('[工作流初始化] 工作流初始化完成');

      } catch (error) {
        console.error('[工作流初始化] 工作流初始化失败:', error);
      }
    },

    // 初始化Begin组件
    initializeBeginComponent(dsl) {
      const beginComponent = dsl.components?.begin;

      if (beginComponent) {
        // 确保Begin组件有完整的输出结构
        const welcomeMessage = beginComponent.obj?.params?.prologue ||
                              "Hi! I'm your smart assistant. What can I do for you?";

        if (!beginComponent.obj.output?.content?.['0']?.content) {
          beginComponent.obj.output = {
            content: {
              "0": {
                content: welcomeMessage
              }
            }
          };
        }

        console.log('[工作流初始化] Begin组件初始化完成');
      }
    },

    // 初始化Retrieval组件
    initializeRetrievalComponents(dsl) {
      Object.values(dsl.components).forEach(component => {
        if (component.obj.component_name === 'Retrieval') {
          // 确保Retrieval组件有完整的输出结构
          if (!component.obj.output) {
            component.obj.output = {
              component_id: { "0": "" },
              content: { "0": "" }
            };
          }

          // 确保有知识库配置
          if (!component.obj.params?.kb_ids || component.obj.params.kb_ids.length === 0) {
            console.warn('[工作流初始化] Retrieval组件缺少知识库配置');
          }

          console.log('[工作流初始化] Retrieval组件初始化完成');
        }
      });
    },

    // 初始化关键词提取组件
    async initializeKeywordExtractComponents(dsl) {
      console.log('[关键词初始化] 开始初始化关键词提取组件');

      const keywordComponents = Object.keys(dsl.components).filter(nodeId =>
        dsl.components[nodeId].obj.component_name === 'KeywordExtract'
      );

      console.log('[关键词初始化] 找到关键词组件:', keywordComponents);

      for (const nodeId of keywordComponents) {
        const component = dsl.components[nodeId];

        try {
          console.log(`[关键词初始化] 处理组件 ${nodeId}:`, component);

          // 确保关键词组件有完整的输出结构
          if (!component.obj.output) {
            component.obj.output = {
              component_id: { "0": nodeId },
              content: { "0": "" }
            };
          }

          // 获取输入文本进行关键词提取
          const inputText = this.getKeywordExtractInputText(component, dsl);
          console.log(`[关键词初始化] 组件 ${nodeId} 的输入文本:`, inputText);

          if (inputText && inputText.trim()) {
            // 执行关键词提取
            const extractionResult = await this.executeKeywordExtraction(nodeId, component, inputText);

            // 🔥 不再更新KeywordExtract的输出，让RAGFlow动态生成
            console.log(`[关键词初始化] 组件 ${nodeId} 跳过输出更新，让RAGFlow处理`);
            if (extractionResult.success) {
              console.log(`[关键词初始化] 组件 ${nodeId} 关键词提取成功（仅用于调试）:`, extractionResult.keywords);
            } else {
              console.error(`[关键词初始化] 组件 ${nodeId} 关键词提取失败:`, extractionResult.error);
            }
          } else {
            console.log(`[关键词初始化] 组件 ${nodeId} 没有有效的输入文本，跳过提取`);
          }

        } catch (error) {
          console.error(`[关键词初始化] 处理组件 ${nodeId} 时发生错误:`, error);
        }
      }

      console.log('[关键词初始化] 关键词提取组件初始化完成');
    },

    // 获取关键词提取组件的输入文本
    getKeywordExtractInputText(component, dsl) {
      console.log('[关键词输入] 获取关键词提取组件的输入文本');

      // 优先检查是否有Answer组件的输入（循环依赖情况）
      if (component.obj.inputs && component.obj.inputs.length > 0) {
        // 查找Answer组件的输入
        const answerInput = component.obj.inputs.find(input =>
          input.component_id && input.component_id.includes('Answer')
        );

        if (answerInput && answerInput.content && answerInput.content.includes('USER:')) {
          console.log('[关键词输入] 从Answer组件获取对话历史:', answerInput.content);
          return answerInput.content;
        }

        // 如果没有Answer输入，检查其他输入
        const input = component.obj.inputs[0];
        if (input.content && input.content.includes('USER:')) {
          console.log('[关键词输入] 从inputs获取对话文本:', input.content);
          return input.content;
        }
      }

      // 从上游组件获取输入
      if (component.upstream && component.upstream.length > 0) {
        const upstreamId = component.upstream[0];
        const upstreamComponent = dsl.components[upstreamId];

        if (upstreamComponent) {
          console.log(`[关键词输入] 从上游组件 ${upstreamId} 获取输入`);

          // 如果上游是Begin组件，获取其prologue
          if (upstreamComponent.obj.component_name === 'Begin') {
            const prologue = upstreamComponent.obj.params?.prologue || '';
            console.log('[关键词输入] 从Begin组件获取prologue:', prologue);
            return `ASSISTANT:${prologue}\nUSER:请提取关键词`;
          }

          // 如果上游是Answer组件，构造对话历史
          if (upstreamComponent.obj.component_name === 'Answer') {
            const beginComponent = dsl.components['begin'];
            const prologue = beginComponent?.obj.params?.prologue || 'Hi! I\'m your smart assistant. What can I do for you?';
            const sampleUserInput = '我现在想要你描述一下售前的一些注意事项';

            const dialogHistory = `ASSISTANT:${prologue}\nUSER:${sampleUserInput}`;
            console.log('[关键词输入] 构造的对话历史:', dialogHistory);
            return dialogHistory;
          }
        }
      }

      // 如果没有找到输入，返回默认文本
      const defaultText = 'ASSISTANT:Hi! I\'m your smart assistant. What can I do for you?\nUSER:我现在想要你描述一下售前的一些注意事项';
      console.log('[关键词输入] 使用默认文本:', defaultText);
      return defaultText;
    },

    // 执行关键词提取
    async executeKeywordExtraction(nodeId, component, inputText) {
      console.log(`[关键词提取] 开始执行组件 ${nodeId} 的关键词提取`);
      console.log(`[关键词提取] 输入文本:`, inputText);
      console.log(`[关键词提取] 组件参数:`, component.obj.params);

      try {
        const params = {
          text: inputText,
          llm_id: component.obj.params?.llm_id || 'qwen-max@Tongyi-Qianwen',
          top_n: component.obj.params?.top_n || 5,
          temperature: component.obj.params?.temperature || 0.1,
          max_tokens: component.obj.params?.max_tokens || 256,
          frequency_penalty: component.obj.params?.frequency_penalty || 0.7,
          presence_penalty: component.obj.params?.presence_penalty || 0.4,
          top_p: component.obj.params?.top_p || 0.3,
          component_id: nodeId
        };

        console.log(`[关键词提取] API调用参数:`, params);

        const result = await extractKeywords(params);

        console.log(`[关键词提取] 组件 ${nodeId} 提取结果:`, result);

        return result;

      } catch (error) {
        console.error(`[关键词提取] 组件 ${nodeId} 提取失败:`, error);
        return {
          success: false,
          keywords: '',
          component_id: nodeId,
          error: error.message
        };
      }
    },

    // 验证组件间连接完整性
    validateComponentConnections(dsl) {
      const components = dsl.components;
      let isValid = true;

      Object.keys(components).forEach(nodeId => {
        const component = components[nodeId];

        // 验证上游连接
        component.upstream.forEach(upstreamId => {
          if (!components[upstreamId]) {
            console.error(`[工作流验证] 组件${nodeId}的上游组件${upstreamId}不存在`);
            isValid = false;
          }
        });

        // 验证下游连接
        component.downstream.forEach(downstreamId => {
          if (!components[downstreamId]) {
            console.error(`[工作流验证] 组件${nodeId}的下游组件${downstreamId}不存在`);
            isValid = false;
          }
        });
      });

      if (isValid) {
        console.log('[工作流验证] 组件连接验证通过');
      } else {
        console.error('[工作流验证] 组件连接验证失败');
      }

      return isValid;
    },

    // 保存包含执行记录的DSL
    async saveWorkflowWithExecutionRecords() {
      try {
        const updateData = {
          title: this.currentAgentName || 'Untitled Workflow',
          description: `工作流 - ${this.currentAgentName || 'Untitled'}`,
          dsl: this.currentWorkflowDSL
        };

        console.log('[保存执行记录] 准备发送的更新数据:', updateData);

        const response = await updateAgent(this.currentAgentId, updateData);
        console.log('[保存执行记录] API响应:', response);

        if (response.data && response.data.retcode === 0) {
          console.log('[保存执行记录] 工作流执行记录保存成功');
        } else {
          console.error('[保存执行记录] 保存失败:', response.data);
        }
      } catch (error) {
        console.error('[保存执行记录] 保存执行记录失败:', error);
      }
    },

    // 显示注释编辑器
    showCommentEditor(commentData) {
      console.log('Received edit-comment event:', commentData);
      this.currentComment = {
        id: commentData.id,
        title: commentData.title,
        content: commentData.content,
        onSave: commentData.onSave
      };
      this.commentDialogVisible = true;
    },

    // 显示循环节点编辑器
    showLoopEditor(loopData) {
      console.log('Received edit-loop event:', loopData);
      this.currentLoop = {
        id: loopData.id,
        title: loopData.title,
        content: loopData.content,
        onSave: loopData.onSave
      };
      this.loopDialogVisible = true;
    },

    // 显示循环项编辑器
    showIterationItemEditor(iterationItemData) {
      console.log('Received edit-iteration-item event:', iterationItemData);
      this.currentIterationItem = {
        id: iterationItemData.id,
        title: iterationItemData.title || 'IterationItem',
        settings: {
          variable: iterationItemData.settings?.variable || '',
          source: iterationItemData.settings?.source || 'text_segments'
        },
        onSave: iterationItemData.onSave
      };
      this.iterationItemDialogVisible = true;
    },

    // 显示问题分类编辑器
    showClassificationEditor(classificationData) {
      console.log('Received edit-classification event:', classificationData);
      this.currentClassification = {
        id: classificationData.id,
        title: classificationData.title,
        categories: classificationData.categories,
        onSave: classificationData.onSave
      };
      this.classificationDialogVisible = true;
    },

    // 显示模板转换编辑器
    showTemplateEditor(templateData) {
      console.log('Received edit-template event:', templateData);
      this.currentTemplate = {
        id: templateData.id,
        title: templateData.title,
        description: templateData.description,
        content: templateData.content,
        onSave: templateData.onSave
      };
      this.templateDialogVisible = true;
    },

    // 显示条件编辑器
    showConditionEditor(conditionData) {

      // 确保存在 cases 数组并进行深拷贝，完全脱离 Vue 响应式系统
      const casesArray = conditionData.cases ? JSON.parse(JSON.stringify(conditionData.cases)) : [];

      // 重置节点数据，确保没有响应式依赖
      this.selectedNode = null;

      // 设置当前选中的节点，确保对 cases 数组进行深拷贝
      this.selectedNode = {
        id: conditionData.id,
        type: 'conditions',
        data: {
          title: conditionData.title,
          elseAction: conditionData.elseAction,
          cases: casesArray,
          type: 'conditions'
        }
      };

      // 打开节点抽屉
      this.nodeDrawerVisible = true;

      // 保存原始的 onSave 回调
      const originalOnSave = conditionData.onSave;

      // 设置节点数据更新后的回调
      this.$nextTick(() => {
        if (this.$refs.nodeDrawer) {
          this.$refs.nodeDrawer.customSaveCallback = null; // 清除之前的回调
          this.$refs.nodeDrawer.setCustomSaveCallback((data) => {
            // 确保存在 cases 数组并进行深拷贝，完全脱离 Vue 响应式系统
            const savedCases = data.cases ? JSON.parse(JSON.stringify(data.cases)) : [];

            if (typeof originalOnSave === 'function') {
              // 调用原始的 onSave 回调，确保传递深拷贝的 cases 数组
              originalOnSave(
                conditionData.id,
                data.elseAction,
                savedCases,
                data.title
              );
            }
          });
        }
      });
    },

    // 切换侧边栏显示状态
    toggleSidebar() {
      this.sidebarCollapsed = !this.sidebarCollapsed;
      // 调整图表大小以适应新的布局
      this.$nextTick(() => {
        if (this.$refs.x6Graph) {
          this.$refs.x6Graph.resizeGraph();
        }
      });
    },

    // 显示全局提示框
    showGlobalTooltip(data) {
      this.globalTooltip.title = data.title;
      this.globalTooltip.content = data.content;
      this.globalTooltip.style = data.style;
      this.globalTooltip.visible = true;
    },

    // 隐藏全局提示框
    hideGlobalTooltip() {
      this.globalTooltip.visible = false;
    },

    // 处理节点选中
    handleNodeSelected(nodeData) {
      console.log('App接收到节点选择事件:', nodeData);

      if (nodeData) {
        console.log('节点选中:', nodeData);
        this.selectedNode = nodeData;

        // 打开抽屉
        this.nodeDrawerVisible = true;
      } else {
        this.selectedNode = null;
        this.nodeDrawerVisible = false;
      }
    },

    // 关闭节点抽屉
    closeNodeDrawer() {
      this.nodeDrawerVisible = false;

      // 清除节点选择
      if (this.$refs.x6Graph) {
        this.$refs.x6Graph.clearSelection();
      }

      // 清空选中节点数据
      this.selectedNode = null;
    },

    // 处理抽屉关闭前的逻辑
    handleDrawerClosed() {
      // 确保抽屉完全关闭后也会清除节点选择
      console.log('抽屉已完全关闭');
      if (this.$refs.x6Graph) {
        this.$refs.x6Graph.clearSelection();
        this.selectedNode = null;
      }
    },

    // 保存节点设置
    saveNodeSettings(data) {
      if (!this.$refs.x6Graph) return;

      console.log('保存节点设置:', data);

      // 更新节点数据
      this.$refs.x6Graph.updateNodeData(data.nodeId, data.data);

      // 显示保存成功提示
      this.$message({
        message: '设置已保存',
        type: 'success',
        duration: 2000
      });
    },

    // 显示添加变量对话框
    showAddVariableDialog() {
      this.addVariableDialogVisible = true;
    },

    // 添加新变量
    addNewVariable(variable) {
      // 如果当前选中节点是关键词节点，添加变量
      if (this.selectedNode && this.selectedNode.type === 'keywords') {
        // 更新节点数据
        const nodeData = this.selectedNode.data || {};
        if (!Array.isArray(nodeData.variables)) {
          nodeData.variables = [];
        }
        nodeData.variables.push(variable);

        // 更新节点
        if (this.$refs.x6Graph) {
          this.$refs.x6Graph.updateNodeData(this.selectedNode.id, {
            variables: nodeData.variables
          });
        }
      }

      // 关闭对话框
      this.addVariableDialogVisible = false;

      // 显示成功消息
      this.$message({
        message: '变量添加成功',
        type: 'success',
        duration: 2000
      });
    },
    // 处理运行工作流
    async handleRunWorkflow() {
      console.log('运行工作流');
      console.log('当前Agent ID:', this.currentAgentId);

      // 检查是否有Agent ID
      if (!this.currentAgentId) {
        this.$message({
          message: '未选择Agent，请先选择或创建一个Agent',
          type: 'error'
        });
        return;
      }

      try {
        // 显示保存和初始化的提示
        const loadingMessage = this.$message({
          message: '正在保存工作流并执行关键词提取...',
          type: 'info',
          duration: 0 // 不自动关闭
        });

        // 先自动保存工作流
        await this.handleSave();

        // 关闭保存提示
        loadingMessage.close();

        // 获取当前工作流的DSL数据
        this.currentWorkflowDSL = await this.getCurrentWorkflowDSL();
        console.log('当前工作流DSL:', this.currentWorkflowDSL);

        // 检查DSL中节点的name字段
        if (this.currentWorkflowDSL.graph && this.currentWorkflowDSL.graph.nodes) {
          this.currentWorkflowDSL.graph.nodes.forEach(node => {
            console.log(`[App调试] 节点 ${node.id}:`, {
              'data.name': node.data.name,
              'data.label': node.data.label,
              'type': node.type
            });
          });
        }

        // 执行工作流初始化（生成开场白记录）
        await this.executeWorkflowInitialization();

        // 重新保存包含执行记录的DSL
        if (this.currentWorkflowDSL.history && this.currentWorkflowDSL.history.length > 0) {
          console.log('[工作流初始化] 保存包含执行记录的DSL');
          await this.saveWorkflowWithExecutionRecords();
        }

        // 保存成功后显示对话界面
        this.chatDrawerVisible = true;

      } catch (error) {
        console.error('运行工作流失败:', error);
        this.$message({
          message: '保存工作流失败，无法启动对话测试',
          type: 'error'
        });
      }
    },

    // 生成唯一的消息ID
    generateMessageId() {
      return Date.now().toString(36) + Math.random().toString(36).substring(2);
    },

    // 处理图表数据以适配保存格式，特别是Categorize组件的边连接
    processGraphDataForSave(graphData, components) {
      const processedData = { ...graphData };

      // 处理edges，特别是Categorize组件的连接
      processedData.edges = graphData.edges.map(edge => {
        const sourceComponent = components[edge.source];

        // 如果源组件是Categorize类型，需要特殊处理
        if (sourceComponent && sourceComponent.obj.component_name === 'Categorize') {
          return this.processCategorizeEdge(edge, sourceComponent, components);
        }

        // 其他组件保持原有格式
        return edge;
      });

      console.log('[边格式处理] 处理后的edges:', processedData.edges);
      return processedData;
    },

    // 处理Categorize组件的边连接
    processCategorizeEdge(edge, categorizeComponent, components) {
      const targetComponent = components[edge.target];

      if (!targetComponent) {
        console.warn('[边格式处理] 找不到目标组件:', edge.target);
        return edge;
      }

      // 从category_description中找到对应的分类名称
      const categoryDesc = categorizeComponent.obj.params?.category_description || {};
      let categoryName = null;

      // 根据目标组件ID找到对应的分类名称
      Object.keys(categoryDesc).forEach(catName => {
        if (categoryDesc[catName].to === edge.target) {
          categoryName = catName;
        }
      });

      if (!categoryName) {
        console.warn('[边格式处理] 无法找到分类名称，使用默认处理');
        return edge;
      }

      // 构建新的edge格式
      const processedEdge = {
        ...edge,
        id: `xy-edge__${edge.source}${categoryName}-${edge.target}c`,
        sourceHandle: categoryName, // 使用分类名称作为sourceHandle
      };

      console.log('[边格式处理] Categorize边处理结果:', {
        原始: edge,
        处理后: processedEdge,
        分类名称: categoryName
      });

      return processedEdge;
    },

    // 获取当前工作流的DSL数据
    async getCurrentWorkflowDSL() {
      if (this.$refs.x6Graph) {
        // 获取图表数据
        const graphData = this.$refs.x6Graph.getGraphData();

        // 构建完整的DSL数据（包含components）
        // 🔥 传递上下文，表明这是初始状态（未对话）
        const context = { isAfterConversation: false };
        const components = await buildComponentsFromGraph(graphData, context);

        const dslData = {
          answer: [],
          components: components,
          embed_id: "text-embedding-v2@Tongyi-Qianwen", // 添加嵌入模型ID
          graph: graphData,
          history: [],
          messages: [],
          path: [], // 添加执行路径
          reference: [],
          rerank: false
        };

        console.log('[getCurrentWorkflowDSL] 构建的完整DSL:', dslData);
        return dslData;
      }
      return {};
    },
    async handleSave() {
      if (!this.currentAgentId) {
        this.$message({ message: '未选择Agent，无法保存', type: 'error' });
        throw new Error('未选择Agent，无法保存');
      }

      // 设置保存状态
      this.isSaving = true;
      console.log('开始保存工作流，Agent ID:', this.currentAgentId);

      // 显示保存中的提示
      const loadingMessage = this.$message({
        message: '正在保存工作流和对话历史...',
        type: 'info',
        duration: 0,
        showClose: false
      });

      try {
        // 获取图表数据
        const graphData = this.$refs.x6Graph.getGraphData();
        console.log('获取到的图表数据:', graphData);

        // 获取对话历史数据
        const conversationHistory = this.$refs.x6Graph ? this.$refs.x6Graph.getCurrentConversationForNodes() : '';
        console.log('获取到的对话历史:', conversationHistory);

        // 构建上下文信息
        const context = {
          conversationHistory: conversationHistory,
          isAfterConversation: !!conversationHistory  // 🔥 如果有对话历史，表明是对话后状态
        };

        const components = await buildComponentsFromGraph(graphData, context);
        console.log('构建的组件数据:', components);

        // 如果有对话历史，更新相关组件的输入数据
        if (conversationHistory) {
          this.updateComponentsWithConversationHistory(components, conversationHistory);
        }

        // 验证DSL完整性
        const validation = this.validateDSLIntegrity(components);
        console.log('[保存] DSL完整性验证:', validation);

        if (!validation.isValid) {
          console.error('[保存] DSL验证失败:', validation.errors);
          this.$message({
            message: `DSL验证失败: ${validation.errors.join(', ')}`,
            type: 'error'
          });
          // 不阻止保存，但给出警告
        }

        if (validation.warnings.length > 0) {
          console.warn('[保存] DSL验证警告:', validation.warnings);
        }

        // 调试DSL保存数据
        const debugReport = debugDSLSave(components, conversationHistory, this.$refs.x6Graph?.sessionId);

        // 检查对话历史完整性
        const integrityCheck = checkConversationIntegrity(components);
        console.log('[保存] 对话历史完整性检查:', integrityCheck);
        console.log('[保存] DSL调试报告:', debugReport.summary);
        console.log('[保存] DSL验证统计:', validation.stats);

        // 详细调试Retrieval节点
        Object.keys(components).forEach(nodeId => {
          if (components[nodeId].obj.component_name === 'Retrieval') {
            console.log(`[DSL调试] Retrieval节点${nodeId}详细信息:`, {
              params: components[nodeId].obj.params,
              upstream: components[nodeId].upstream,
              downstream: components[nodeId].downstream,
              hasInputs: !!components[nodeId].obj.inputs,
              hasOutput: !!components[nodeId].obj.output
            });
          }
        });

        // 预填充基础执行状态，确保数据完整性
        const initialHistory = [
          ["assistant", "Hi! I'm your smart assistant. What can I do for you?"]
        ];

        const initialMessages = [
          {
            content: "Hi! I'm your smart assistant. What can I do for you?",
            id: this.generateMessageId(),
            role: "assistant"
          }
        ];

        const initialPath = [["begin"]];

        // 处理边的格式，特别是Categorize组件的连接
        const processedGraphData = this.processGraphDataForSave(graphData, components);

        const dsl = {
          answer: [],
          components,
          embed_id: "text-embedding-v2@Tongyi-Qianwen",
          graph: processedGraphData,
          history: initialHistory, // 预填充初始对话历史
          messages: initialMessages, // 预填充初始消息
          path: initialPath, // 预填充初始执行路径
          reference: [],
          rerank: false
        };

        console.log('准备保存的DSL数据:', dsl);

        // 执行工作流初始化，确保数据完整性
        await this.executeWorkflowInitialization(dsl);

        // 详细调试每个组件的结构
        console.log('=== DSL组件详细调试 ===');
        Object.keys(dsl.components).forEach(nodeId => {
          const component = dsl.components[nodeId];
          console.log(`组件 ${nodeId}:`, {
            component_name: component.obj.component_name,
            hasInputs: !!component.obj.inputs,
            hasOutput: !!component.obj.output,
            paramsKeys: Object.keys(component.obj.params || {}),
            upstream: component.upstream,
            downstream: component.downstream
          });
        });

        // 使用封装好的API更新Agent
        const updateData = {
          title: this.currentAgentName || '未命名工作流',
          description: `工作流 - ${this.currentAgentName || '未命名'}`,
          dsl: dsl
        };

        console.log('准备发送的更新数据:', updateData);

        const res = await updateAgent(this.currentAgentId, updateData);
        console.log('API响应:', res);

        if (res.data && res.data.code === 0) {
          // 关闭loading提示和重置保存状态
          loadingMessage.close();
          this.isSaving = false;

          // 显示保存成功提示
          this.$message({
            message: '工作流和对话历史保存成功！',
            type: 'success',
            duration: 3000
          });

          // 保存对话历史到本地存储
          if (this.$refs.x6Graph && this.$refs.x6Graph.sessionId) {
            const sessionStats = this.$refs.x6Graph.getSessionStats();
            console.log('[保存] 对话历史统计:', sessionStats);
            console.log('[保存] 对话历史已自动保存到本地存储');
          }

          console.log('[保存] 工作流保存完成，包含对话历史');
          return res; // 返回成功结果
        } else {
          // 关闭loading提示和重置保存状态
          loadingMessage.close();
          this.isSaving = false;

          console.error('保存失败，响应数据:', res.data);
          const errorMessage = (res.data && res.data.message) ? res.data.message : '保存失败';
          this.$message({
            message: errorMessage,
            type: 'error'
          });
          throw new Error(errorMessage);
        }
      } catch (error) {
        // 关闭loading提示和重置保存状态
        loadingMessage.close();
        this.isSaving = false;

        console.error('保存异常:', error);
        const errorMessage = error.response?.data?.message || error.message || '保存异常';
        this.$message({
          message: errorMessage,
          type: 'error'
        });
        throw error; // 重新抛出错误，让调用者能够捕获
      }
    },

    /**
     * 更新组件的对话历史数据（仅用于调试，不影响DSL结构）
     * @param {Object} components - 组件数据
     * @param {string} conversationHistory - 对话历史
     */
    updateComponentsWithConversationHistory(components, conversationHistory) {
      console.log('[保存] 记录对话历史数据（不修改DSL结构）');

      if (!conversationHistory) {
        console.log('[保存] 无对话历史');
        return;
      }

      // 仅记录对话历史信息，不修改组件的inputs
      console.log('[保存] 对话历史长度:', conversationHistory.length);
      console.log('[保存] 对话轮数:', conversationHistory.split('\n').filter(line =>
        line.startsWith('USER:') || line.startsWith('ASSISTANT:')
      ).length);

      // 统计需要对话历史的组件
      const keywordExtractComponents = Object.keys(components).filter(nodeId =>
        components[nodeId].obj.component_name === 'KeywordExtract'
      );

      const retrievalComponents = Object.keys(components).filter(nodeId =>
        components[nodeId].obj.component_name === 'Retrieval'
      );

      console.log('[保存] KeywordExtract组件:', keywordExtractComponents);
      console.log('[保存] Retrieval组件:', retrievalComponents);
      console.log('[保存] 对话历史将在运行时由RAGFlow自动处理');
    },

    /**
     * 验证DSL组件的完整性
     * @param {Object} components - 组件数据
     * @returns {Object} 验证结果
     */
    validateDSLIntegrity(components) {
      const validation = {
        isValid: true,
        errors: [],
        warnings: [],
        stats: {
          totalComponents: Object.keys(components).length,
          componentsWithInputs: 0,
          componentsWithOutputs: 0,
          emptyInputComponents: [],
          emptyOutputComponents: []
        }
      };

      Object.keys(components).forEach(nodeId => {
        const component = components[nodeId];
        const componentName = component.obj.component_name;

        // 检查输入
        if (component.obj.inputs && component.obj.inputs.length > 0) {
          validation.stats.componentsWithInputs++;

          // 检查输入内容是否为空
          const hasEmptyInputs = component.obj.inputs.some(input =>
            !input.content || input.content.trim() === ''
          );

          if (hasEmptyInputs) {
            validation.stats.emptyInputComponents.push(nodeId);
            validation.warnings.push(`组件 ${nodeId} 有空的输入内容`);
          }
        } else if (componentName !== 'Begin') {
          // Begin组件可以没有输入，其他组件应该有输入
          validation.stats.emptyInputComponents.push(nodeId);
          validation.warnings.push(`组件 ${nodeId} 缺少输入数据`);
        }

        // 检查输出
        if (component.obj.output && Object.keys(component.obj.output).length > 0) {
          validation.stats.componentsWithOutputs++;

          // 检查输出内容是否为空
          const hasEmptyOutputs = this.checkEmptyOutput(component.obj.output);
          if (hasEmptyOutputs) {
            validation.stats.emptyOutputComponents.push(nodeId);
            validation.warnings.push(`组件 ${nodeId} 有空的输出内容`);
          }
        } else {
          validation.stats.emptyOutputComponents.push(nodeId);
          validation.warnings.push(`组件 ${nodeId} 缺少输出数据`);
        }

        // 检查上下游连接
        if (componentName !== 'Begin' && (!component.upstream || component.upstream.length === 0)) {
          validation.errors.push(`组件 ${nodeId} 缺少上游连接`);
          validation.isValid = false;
        }
      });

      return validation;
    },

    /**
     * 检查输出是否为空
     * @param {Object} output - 输出对象
     * @returns {boolean} 是否为空
     */
    checkEmptyOutput(output) {
      if (!output) return true;

      // 检查不同类型的输出结构
      if (output.content) {
        if (typeof output.content === 'string') {
          return !output.content.trim();
        } else if (typeof output.content === 'object') {
          return Object.values(output.content).every(value => {
            if (typeof value === 'string') {
              return !value.trim();
            } else if (typeof value === 'object' && value.content) {
              return !value.content.trim();
            }
            return false;
          });
        }
      }

      return false;
    },

    /**
     * 测试保存功能（开发调试用）
     */
    testSaveFunction() {
      console.group('🧪 测试保存功能');

      // 模拟用户输入
      if (this.$refs.x6Graph) {
        this.$refs.x6Graph.addUserInputToHistory('测试用户输入：请帮我总结一下销售之后的注意事项');
        this.$refs.x6Graph.addAssistantOutputToHistory('测试助手回复：好的，我来为您总结销售之后的注意事项...');

        // 获取对话历史
        const conversationHistory = this.$refs.x6Graph.getCurrentConversationForNodes();
        console.log('📝 测试对话历史:', conversationHistory);

        // 获取会话统计
        const sessionStats = this.$refs.x6Graph.getSessionStats();
        console.log('📊 会话统计:', sessionStats);
      }

      console.groupEnd();
    },

    /**
     * 测试修复后的DSL构建功能
     */
    testFixedDSLBuild() {
      console.group('🔧 测试修复后的DSL构建功能');

      if (this.$refs.x6Graph) {
        const graphData = this.$refs.x6Graph.getGraphData();
        console.log('📊 当前图表数据:', graphData);

        // 构建DSL组件
        const components = buildComponentsFromGraph(graphData);
        console.log('🏗️ 构建的组件结构:', components);

        // 检查关键修复点
        console.log('🔍 检查修复效果:');

        // 1. 检查循环依赖
        const answerComponents = Object.keys(components).filter(id =>
          components[id].obj.component_name === 'Answer'
        );
        const keywordComponents = Object.keys(components).filter(id =>
          components[id].obj.component_name === 'KeywordExtract'
        );

        if (answerComponents.length > 0 && keywordComponents.length > 0) {
          const answerId = answerComponents[0];
          const keywordId = keywordComponents[0];
          console.log(`✅ 循环依赖检查: ${answerId} -> ${keywordId}`);
          console.log('Answer下游:', components[answerId].downstream);
          console.log('KeywordExtract上游:', components[keywordId].upstream);
          console.log('KeywordExtract query参数:', components[keywordId].obj.params.query);
        }

        // 2. 检查输入内容质量
        Object.keys(components).forEach(nodeId => {
          const component = components[nodeId];
          if (component.obj.inputs && component.obj.inputs.length > 0) {
            component.obj.inputs.forEach((input, index) => {
              const contentPreview = input.content.substring(0, 100);
              console.log(`📝 ${nodeId} 输入${index}: ${contentPreview}${input.content.length > 100 ? '...' : ''}`);
            });
          }
        });

        // 3. 检查Categorize组件输出
        const categorizeComponents = Object.keys(components).filter(id =>
          components[id].obj.component_name === 'Categorize'
        );
        categorizeComponents.forEach(catId => {
          const catComponent = components[catId];
          console.log(`🏷️ Categorize组件 ${catId}:`);
          console.log('  分类配置:', catComponent.obj.params.category_description);
          console.log('  输出:', catComponent.obj.output);
        });
      }

      console.groupEnd();
    },

    /**
     * 测试Retrieval组件输入内容修复
     */
    testRetrievalInputFix() {
      console.group('🔧 测试Retrieval组件输入内容修复');

      if (this.$refs.x6Graph) {
        const graphData = this.$refs.x6Graph.getGraphData();
        const components = buildComponentsFromGraph(graphData);

        console.log('🔍 检查Retrieval组件的输入内容:');

        // 检查所有Retrieval组件
        Object.keys(components).forEach(nodeId => {
          const component = components[nodeId];
          if (component.obj.component_name === 'Retrieval') {
            console.log(`📋 ${nodeId}:`);
            console.log('  上游组件:', component.upstream);
            console.log('  输入内容:', component.obj.inputs);

            if (component.obj.inputs && component.obj.inputs.length > 0) {
              component.obj.inputs.forEach((input, index) => {
                console.log(`  输入${index}:`, {
                  来源: input.component_id,
                  内容: input.content,
                  内容长度: input.content.length,
                  是否为组件ID: input.content.includes('_') && input.content.length < 20
                });
              });
            }
          }
        });

        // 检查Categorize组件的输出
        console.log('🏷️ 检查Categorize组件的输出:');
        Object.keys(components).forEach(nodeId => {
          const component = components[nodeId];
          if (component.obj.component_name === 'Categorize') {
            console.log(`📋 ${nodeId}:`);
            console.log('  输入内容:', component.obj.inputs);
            console.log('  输出内容:', component.obj.output);
            console.log('  路由配置:', component.obj.params.category_description);
          }
        });
      }

      console.groupEnd();
    },

    /**
     * 测试Categorize组件输入验证修复
     */
    testCategorizeInputValidation() {
      console.group('🔧 测试Categorize组件输入验证修复');

      if (this.$refs.x6Graph) {
        const graphData = this.$refs.x6Graph.getGraphData();
        const conversationHistory = this.$refs.x6Graph.getCurrentConversationForNodes() || '';

        const context = {
          conversationHistory: conversationHistory,
          keywords: "售前, 介绍, 相关事项"
        };

        const components = buildComponentsFromGraph(graphData, context);

        console.log('🔍 检查Categorize组件的输入验证结果:');

        // 检查所有Categorize组件
        Object.keys(components).forEach(nodeId => {
          const component = components[nodeId];
          if (component.obj.component_name === 'Categorize') {
            console.log(`📋 Categorize组件 ${nodeId}:`);
            console.log('  上游组件:', component.upstream);
            console.log('  输入内容:', component.obj.inputs);
            console.log('  输出内容:', component.obj.output);

            if (component.obj.inputs && component.obj.inputs.length > 0) {
              component.obj.inputs.forEach((input, index) => {
                const content = input.content;
                const isValid = content && content.trim() &&
                               (content.includes(',') || content.includes('，') ||
                                content.includes('USER:') ||
                                (!content.includes('_') || content.length > 20 || content.includes(' ')));

                console.log(`  输入${index}:`, {
                  来源: input.component_id,
                  内容: content,
                  内容长度: content.length,
                  格式有效: isValid ? '✅' : '❌',
                  是否为组件ID: content.includes('_') && content.length < 20 && !content.includes(' ') ? '⚠️' : '✅'
                });
              });
            }
          }
        });

        // 检查修复前后的对比
        console.log('🔄 验证修复效果:');
        const categorizeComponents = Object.keys(components).filter(id =>
          components[id].obj.component_name === 'Categorize'
        );

        if (categorizeComponents.length > 0) {
          console.log(`✅ 找到 ${categorizeComponents.length} 个Categorize组件`);
          categorizeComponents.forEach(catId => {
            const catComponent = components[catId];
            const hasValidInputs = catComponent.obj.inputs &&
                                 catComponent.obj.inputs.length > 0 &&
                                 catComponent.obj.inputs[0].content &&
                                 catComponent.obj.inputs[0].content.trim();
            console.log(`  ${catId}: 输入有效性 ${hasValidInputs ? '✅' : '❌'}`);
          });
        } else {
          console.log('⚠️ 未找到Categorize组件');
        }
      }

      console.groupEnd();
    },

    /**
     * 测试KeywordExtract组件输出格式修复
     */
    testKeywordExtractOutputFormat() {
      console.group('🔧 测试KeywordExtract组件输出格式修复');

      if (this.$refs.x6Graph) {
        const graphData = this.$refs.x6Graph.getGraphData();
        const conversationHistory = this.$refs.x6Graph.getCurrentConversationForNodes() || '';

        const context = {
          conversationHistory: conversationHistory,
          userQuery: "帮我介绍一下售前的相关事项"
        };

        const components = buildComponentsFromGraph(graphData, context);

        console.log('🔍 检查KeywordExtract组件的输出格式:');

        // 检查所有KeywordExtract组件
        Object.keys(components).forEach(nodeId => {
          const component = components[nodeId];
          if (component.obj.component_name === 'KeywordExtract') {
            console.log(`📋 KeywordExtract组件 ${nodeId}:`);
            console.log('  输入内容:', component.obj.inputs);
            console.log('  输出内容:', component.obj.output);

            // 验证输出格式
            const output = component.obj.output;
            const outputContent = output?.content?.['0'];

            console.log(`  输出验证:`, {
              有输出: !!output,
              有内容: !!outputContent,
              内容类型: typeof outputContent,
              内容值: outputContent,
              是关键词格式: outputContent && (outputContent.includes(',') || outputContent.includes('，')),
              内容长度: outputContent ? outputContent.length : 0
            });

            // 测试关键词提取功能
            if (component.obj.inputs && component.obj.inputs.length > 0) {
              const inputContent = component.obj.inputs[0].content;
              console.log(`  关键词提取测试:`);
              console.log(`    输入: "${inputContent}"`);

              const extractedKeywords = extractKeywordsFromQuery(inputContent);
              console.log(`    提取结果: "${extractedKeywords}"`);
              console.log(`    是否匹配输出: ${extractedKeywords === outputContent ? '✅' : '❌'}`);
            }

            // 检查下游连接
            const downstreamComponents = Object.keys(components).filter(id => {
              const comp = components[id];
              return comp.upstream && comp.upstream.includes(nodeId);
            });

            console.log(`  下游组件 (${downstreamComponents.length}个):`, downstreamComponents.map(id =>
              `${id}(${components[id].obj.component_name})`
            ));

            // 特别检查Categorize下游
            const categorizeDownstream = downstreamComponents.filter(id =>
              components[id].obj.component_name === 'Categorize'
            );

            if (categorizeDownstream.length > 0) {
              console.log(`  Categorize下游组件接收验证:`);
              categorizeDownstream.forEach(catId => {
                const catComponent = components[catId];
                const keywordInput = catComponent.obj.inputs?.find(input => input.component_id === nodeId);
                console.log(`    ${catId}: 接收内容 = "${keywordInput?.content}"`);
                console.log(`    ${catId}: 内容匹配 = ${keywordInput?.content === outputContent ? '✅' : '❌'}`);
              });
            }
          }
        });

        // 测试关键词提取函数
        console.log('🧪 测试关键词提取函数:');
        const testQueries = [
          "帮我介绍一下售前的相关事项",
          "技术支持怎么联系",
          "产品功能有哪些",
          "价格是多少",
          "安装步骤",
          "普通查询文本"
        ];

        testQueries.forEach(query => {
          const keywords = extractKeywordsFromQuery(query);
          console.log(`  "${query}" -> "${keywords}"`);
        });
      }

      console.groupEnd();
    },

    /**
     * 测试动态输入更新机制
     */
    async testDynamicInputUpdate() {
      console.group('🔧 测试动态输入更新机制');

      if (this.$refs.x6Graph) {
        const graphData = this.$refs.x6Graph.getGraphData();
        const conversationHistory = this.$refs.x6Graph.getCurrentConversationForNodes() || '';

        const context = {
          conversationHistory: conversationHistory,
          userQuery: "帮我介绍一下售前的相关事项",
          keywords: "售前, 介绍, 相关事项"
        };

        console.log('🚀 开始测试动态更新机制');
        console.log('📊 图表数据:', graphData);
        console.log('🎯 上下文:', context);

        try {
          // 执行动态更新
          const components = await buildComponentsFromGraph(graphData, context);

          console.log('✅ 动态更新完成');
          console.log('📦 更新后的组件:', components);

          // 验证更新效果
          console.log('🔍 验证更新效果:');

          // 检查KeywordExtract组件
          const keywordComponents = Object.keys(components).filter(id =>
            components[id].obj.component_name === 'KeywordExtract'
          );

          keywordComponents.forEach(keywordId => {
            const component = components[keywordId];
            console.log(`📝 KeywordExtract ${keywordId}:`);
            console.log('  输入:', component.obj.inputs);
            console.log('  输出:', component.obj.output);

            const outputContent = component.obj.output?.content?.['0'];
            const hasValidOutput = outputContent && outputContent.trim() &&
                                 (outputContent.includes(',') || outputContent.includes('，'));
            console.log(`  输出有效性: ${hasValidOutput ? '✅' : '❌'}`);
          });

          // 检查Categorize组件
          const categorizeComponents = Object.keys(components).filter(id =>
            components[id].obj.component_name === 'Categorize'
          );

          categorizeComponents.forEach(catId => {
            const component = components[catId];
            console.log(`📋 Categorize ${catId}:`);
            console.log('  输入:', component.obj.inputs);

            const hasValidInput = component.obj.inputs && component.obj.inputs.length > 0 &&
                                component.obj.inputs[0].content && component.obj.inputs[0].content.trim();
            console.log(`  输入有效性: ${hasValidInput ? '✅' : '❌'}`);
          });

          // 检查数据传递链
          console.log('🔗 检查数据传递链:');
          const chainValidation = validateInputOutputChain(components);
          console.log(`  链完整性: ${chainValidation.length === 0 ? '✅' : '❌'}`);
          if (chainValidation.length > 0) {
            console.log('  问题列表:', chainValidation);
          }

          // 测试实时更新
          console.log('⚡ 测试实时更新:');
          if (keywordComponents.length > 0) {
            const testNodeId = keywordComponents[0];
            const updatedComponents = await updateComponentInputsRealtime(components, testNodeId, context);
            console.log(`  实时更新影响的组件: ${updatedComponents.length} 个`);
            console.log(`  更新的组件ID:`, updatedComponents);
          }

        } catch (error) {
          console.error('❌ 动态更新测试失败:', error);
        }
      }

      console.groupEnd();
    },

    /**
     * 测试安全动态更新机制
     */
    async testSafeDynamicUpdate() {
      console.group('🔧 测试安全动态更新机制');

      if (this.$refs.x6Graph) {
        const graphData = this.$refs.x6Graph.getGraphData();
        const conversationHistory = this.$refs.x6Graph.getCurrentConversationForNodes() || '';

        const context = {
          conversationHistory: conversationHistory,
          userQuery: "帮我介绍一下售前的相关事项",
          keywords: "售前, 介绍, 相关事项"
        };

        console.log('🚀 开始测试安全动态更新机制');
        console.log('📊 图表数据:', graphData);
        console.log('🎯 上下文:', context);

        try {
          // 执行安全动态更新
          const updateResult = await executeSafeDynamicInputUpdate({}, context);

          console.log('✅ 安全动态更新完成');
          console.log('📦 更新结果:', updateResult);

          if (updateResult.success) {
            console.log('🎉 更新成功！');
          } else {
            console.warn('⚠️ 更新失败，问题:', updateResult.issues);
          }

        } catch (error) {
          console.error('❌ 安全动态更新测试失败:', error);
        }
      }

      console.groupEnd();
    },

  }
}
</script>

<style>
html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
  overflow: hidden;
}

#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.main-container {
  display: flex;
  flex: 1;
  height: calc(100vh - 60px); /* 减去导航栏高度 */
  overflow: hidden;
  position: relative;
}

.workflow-page {
  display: flex;
  width: 100%;
  height: 100%;
}

.agent-page {
  width: 100%;
  height: 100%;
  overflow: auto;
}

.sidebar-container {
  position: relative;
  transition: all 0.3s ease;
  width: 220px;
  z-index: 10;
}

.sidebar-container.collapsed {
  width: 0;
  overflow: visible; /* 改为visible，确保按钮可见 */
}

.sidebar-toggle {
  position: absolute;
  top: 20px;
  right: -15px;
  width: 30px;
  height: 30px;
  background: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 20;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.sidebar-toggle:hover {
  background: #f5f7fa;
  transform: scale(1.1);
}

.sidebar-toggle i {
  color: #606266;
  font-size: 12px;
  transition: all 0.3s ease;
}

.sidebar-toggle:hover i {
  color: #409eff;
}
/* 移除 workflow-toolbar 相关样式 */
</style>







