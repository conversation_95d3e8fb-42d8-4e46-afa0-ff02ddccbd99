<template>
  <el-drawer
    :visible.sync="drawerVisible"
    :with-header="true"
    :title="drawerTitle"
    size="30%"
    direction="rtl"
    :before-close="handleClose"
    @closed="handleClosed"
  >
    <div class="drawer-content">
      <div v-if="node" class="node-description">
        {{ getNodeDescription(node.type) }}
      </div>
      
      <!-- 根据节点类型显示不同的设置表单 -->
      <div v-if="node">

        <!-- 开始节点设置 -->
        <begin-settings
          v-if="node.data.type === 'beginNode' || node.id === 'begin'"
          ref="beginSettings"
          :node-data="node.data"
          @save="saveSettings"
        />
        <!-- 检索节点设置 -->
        <retrieval-settings
          v-if="node.type === 'retrievalNode' || node.type === 'retrieval'"
          :node-data="node.data"
          @save="saveSettings"
        />
        <!-- 优化节点设置 -->
        <optimization-settings
          v-else-if="node.type === 'optimization'"
          :node-data="node.data"
          @save="saveSettings"
        />
        <!-- 关键词节点设置（兼容 keywordNode） -->
        <keywords-settings
          v-else-if="node.type === 'keywords' || node.type === 'keywordNode'"
          :initial-data="nodeSettingsData"
          :graph-data="graphData"
          @update="saveSettings"
          @save="saveSettings"
          @add-variable="showAddVariable"
          @show-variable-dialog="showAddVariable"
          @model-change="handleModelChange"
          @request-graph-data="handleRequestGraphData"
        />
        <!-- 生成回答节点设置 -->
        <generation-settings
          v-else-if="node.type === 'generation'"
          :initial-data="nodeSettingsData"
          @save="saveSettings"
        />
        <!-- 问题分类节点设置 -->
        <classification-settings
          v-else-if="node.type === 'classification' || node.type === 'categorizeNode'"
          :node-data="node.data"
          :graph-data="graphData"
          @save="saveSettings"
          @edit-classification="handleEditClassification"
          @request-graph-data="handleRequestGraphData"
        />
        <!-- 静态消息节点设置 -->
        <message-settings
          v-else-if="['message', 'messageNode', 'Message', 'staticMessage', '静态消息', 'message_node'].includes(node.type)"
          :node-data="node.data"
          @save="saveSettings"
        />
        <!-- 条件节点设置 -->
        <condition-settings
          v-if="node.type === 'conditions' || node.type === 'switchNode'"
          :node-data="node.data"
          @update:nodeData="updateConditionSettings"
        />
        <!-- 代码节点设置（兼容 ragNode 代码类型） -->
        <code-settings
          v-else-if="node.type === 'code' || (node.type === 'ragNode' && node.data && node.data.form && node.data.form.lang && node.data.form.script)"
          v-model="codeSettingsData"
        />
        <!-- 模板转换节点设置（兼容 templateNode） -->
        <template-settings
          v-else-if="node.type === 'template' || node.type === 'templateNode'"
          v-model="localTemplateForm"
          @input="onTemplateSettingsInput"
        />
        <!-- 循环节点设置 -->
        <loop-settings
          v-else-if="node.type === 'group' || node.type === 'loop'"
          v-model="localLoopData"
          @input="onLoopSettingsInput"
        />
        <!-- 通用节点设置 -->
      </div>
      
      <div v-else class="empty-state">
        <i class="el-icon-info"></i>
        <p>请选择一个节点来查看和编辑其属性</p>
      </div>
    </div>
    
    <!-- 变量添加对话框 -->
    <variable-dialog 
      :visible="variableDialogVisible"
      @close="variableDialogVisible = false"
      @add="addVariable"
    />
  </el-drawer>
</template>

<script>
import OptimizationSettings from './node-settings/OptimizationSettings.vue';
import KeywordsSettings from './node-settings/KeywordsSettings.vue';
import GenerationSettings from './node-settings/GenerationSettings.vue';
import ClassificationSettings from './node-settings/ClassificationSettings.vue';
import ConditionSettings from './node-settings/ConditionSettings.vue';
// import CommonSettings from './node-settings/CommonSettings.vue';
import VariableDialog from '../dialogs/VariableDialog.vue';
import CodeSettings from './node-settings/CodeSettings.vue';
import MessageSettings from './node-settings/MessageSettings.vue';
import RetrievalSettings from './node-settings/RetrievalSettings.vue';
import LoopSettings from './node-settings/LoopSettings.vue';
import TemplateSettings from './node-settings/TemplateSettings.vue';
import BeginSettings from './node-settings/BeginSettings.vue';

export default {
  name: 'NodeDrawer',
  components: {
    OptimizationSettings,
    KeywordsSettings,
    GenerationSettings,
    ClassificationSettings,
    ConditionSettings,
    // CommonSettings,
    VariableDialog,
    CodeSettings,
    MessageSettings,
    RetrievalSettings,
    LoopSettings,
    TemplateSettings,
    BeginSettings
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    node: {
      type: Object,
      default: null
    }
  },
  computed: {
    drawerVisible: {
      get() {
        return this.visible;
      },
      set(value) {
        if (!value) {
          this.$emit('close');
        }
      }
    },
    drawerTitle() {
      if (!this.node) return '节点设置';
      const nodeType = this.node.data?.type || this.node.type;
      return `${this.getNodeTypeName(nodeType)} 设置`;
    },
    graphData() {
      // 获取图表数据，用于传递给子组件
      console.log('[NodeDrawer] 尝试获取图表数据...');

      try {
        // 方案1: 通过父组件(App.vue)的$refs.x6Graph获取
        if (this.$parent && this.$parent.$refs && this.$parent.$refs.x6Graph) {
          console.log('[NodeDrawer] 通过$parent.$refs.x6Graph获取图表数据');
          const data = this.$parent.$refs.x6Graph.getGraphData();
          console.log('[NodeDrawer] 获取到的图表数据:', data);
          return data;
        }

        // 方案2: 通过根组件获取
        if (this.$root && this.$root.$refs && this.$root.$refs.x6Graph) {
          console.log('[NodeDrawer] 通过$root.$refs.x6Graph获取图表数据');
          const data = this.$root.$refs.x6Graph.getGraphData();
          console.log('[NodeDrawer] 获取到的图表数据:', data);
          return data;
        }

        console.warn('[NodeDrawer] 无法找到x6Graph引用');
        console.log('[NodeDrawer] $parent:', this.$parent);
        console.log('[NodeDrawer] $parent.$refs:', this.$parent?.$refs);

        return null;
      } catch (error) {
        console.error('[NodeDrawer] 获取图表数据失败:', error);
        return null;
      }
    },
  },
  data() {
    return {
      variableDialogVisible: false,
      nodeSettingsData: {
        name: '',
        description: '',
        selectedModel: '默认大模型',
        settings: {},
        topN: 5,
        variables: [],
        systemPrompt: '',
        tools: [],
        historySize: 5,
        enableCitation: true
      },
      customSaveCallback: null, // 添加自定义保存回调
      codeSettingsData: null, // 新增本地变量
      localLoopData: null, // 循环节点本地副本
      localTemplateForm: null // 模板节点本地副本
    };
  },
  watch: {
    node: {
      handler(newVal) {
        if (newVal) {
          this.initNodeSettings(newVal);
          if (newVal.type === 'code') {
            // 深拷贝，避免直接引用 props
            this.codeSettingsData = JSON.parse(JSON.stringify(newVal.data.settings || {}));
          }
          if (newVal.type === 'group' || newVal.type === 'loop') {
            this.localLoopData = JSON.parse(JSON.stringify(newVal.data || {}));
          }
          if (newVal.type === 'template' || newVal.type === 'templateNode') {
            this.localTemplateForm = JSON.parse(JSON.stringify(newVal.data.form || {}));
          }
        }
      },
      immediate: true,
      deep: true
    },
    codeSettingsData: {
      handler(val) {
        if (this.node && this.node.type === 'code') {
          // 通过事件通知父组件更新settings
          this.$emit('update-node-settings', { ...val });
        }
      },
      deep: true
    }
  },
  methods: {
    initNodeSettings(node) {
      // 初始化节点设置数据
      if (node.type === 'optimization') {
        this.nodeSettingsData = {
          name: node.data.modelName || '问题优化',
          selectedModel: node.data.selectedModel || '默认大模型',
          settings: {
            correctSpelling: node.data.settings?.correctSpelling !== undefined ? 
              node.data.settings.correctSpelling : true,
            expandQuery: node.data.settings?.expandQuery !== undefined ? 
              node.data.settings.expandQuery : false
          }
        };
      } else if (node.type === 'keywords') {
        this.nodeSettingsData = {
          name: node.data.modelName || node.data.name || '关键词',
          selectedModel: node.data.selectedModel || node.data.form?.llm_id || '默认大模型',
          topN: node.data.topN || node.data.form?.top_n || 5,
          variables: Array.isArray(node.data.variables) ? [...node.data.variables] : [
            { name: 'deepseek-chat', source: 'DeepSeek' }
          ],
          form: {
            frequencyPenaltyEnabled: node.data.form?.frequencyPenaltyEnabled || false,
            frequency_penalty: node.data.form?.frequency_penalty || 0.7,
            llm_id: node.data.form?.llm_id || node.data.selectedModel || "qwen-max@Tongyi-Qianwen",
            maxTokensEnabled: node.data.form?.maxTokensEnabled || false,
            max_tokens: node.data.form?.max_tokens || 256,
            presencePenaltyEnabled: node.data.form?.presencePenaltyEnabled || false,
            presence_penalty: node.data.form?.presence_penalty || 0.4,
            query: node.data.form?.query || [],
            temperature: node.data.form?.temperature || 0.1,
            temperatureEnabled: node.data.form?.temperatureEnabled || false,
            topPEnabled: node.data.form?.topPEnabled || false,
            top_n: node.data.form?.top_n || node.data.topN || 5,
            top_p: node.data.form?.top_p || 0.3
          }
        };
      } else if (node.type === 'generation') {
        console.log('[NodeDrawer] Generation节点数据:', node.data);
        console.log('[NodeDrawer] Generation节点form:', node.data.form);
        console.log('[NodeDrawer] 各个prompt字段值:');
        console.log('  - node.data.systemPrompt:', node.data.systemPrompt);
        console.log('  - node.data.form?.prompt:', node.data.form?.prompt);
        console.log('  - node.data.prompt:', node.data.prompt);

        const finalPrompt = node.data.systemPrompt ||
                           node.data.form?.prompt ||
                           node.data.prompt ||
                           "请总结以下段落。注意数字，不要胡编乱造。段落如下：\n{input}\n以上就是你需要总结的内容。";

        console.log('[NodeDrawer] 最终使用的prompt:', finalPrompt);

        this.nodeSettingsData = {
          name: node.data.modelName || node.data.name || '生成回答',
          selectedModel: node.data.selectedModel || '默认大模型',
          systemPrompt: finalPrompt,
          tools: Array.isArray(node.data.tools) ? [...node.data.tools] : [],
          historySize: node.data.historySize || node.data.form?.message_history_window_size || 5,
          enableCitation: node.data.enableCitation !== undefined ?
            node.data.enableCitation : (node.data.form?.cite !== undefined ? node.data.form.cite : true)
        };

        console.log('[NodeDrawer] 设置的nodeSettingsData:', this.nodeSettingsData);
      } else {
        // 通用节点设置
        this.nodeSettingsData = {
          name: node.data.modelName || '',
          description: node.data.description || ''
        };
      }
    },
    
    getNodeTitle() {
      if (!this.node) return '';
      
      // 根据节点类型返回不同的标题
      switch (this.node.type) {
        case 'optimization':
          return '问题优化';
        case 'retrieval':
          return '知识检索';
        case 'generation':
          return '生成回答';
        case 'dialogue':
          return '对话';
        case 'classification':
          return '问题分类';
        case 'message':
          return '静态消息';
        case 'loop':
          return '循环';
        default:
          // 检查是否是循环项节点
          if (this.node.data && this.node.data.isIterationItem) {
            return '循环项';
          }
          return this.node.data.modelName || '节点属性';
      }
    },
    
    handleSettingsUpdate(newData) {
      // 更新节点设置数据
      this.nodeSettingsData = { ...this.nodeSettingsData, ...newData };
    },
    
    handleModelChange(model) {
      // 显示一个临时提示，告知用户已应用更改
      this.$message({
        message: `已将模型更改为: ${model}`,
        type: 'success',
        duration: 2000
      });
    },
    
    saveNodeSettings() {
      // 发送保存事件
      this.$emit('save', {
        nodeId: this.node.id,
        data: this.nodeSettingsData
      });
      
      // 关闭抽屉
      this.$emit('close');
    },
    
    handleClose(done) {
      // 在关闭前保存Begin节点的数据
      if (this.node && (this.node.data.type === 'beginNode' || this.node.id === 'begin')) {
        this.saveBeginNodeData();
      }

      // 关闭抽屉
      done();
      // 触发关闭事件
      this.$emit('close');
    },
    
    handleClosed() {
      // 触发完全关闭事件
      this.$emit('closed');
    },
    
    addVariable(variable) {
      // 如果当前是关键词节点，添加变量
      if (this.node && this.node.type === 'keywords') {
        if (!Array.isArray(this.nodeSettingsData.variables)) {
          this.nodeSettingsData.variables = [];
        }
        this.nodeSettingsData.variables.push(variable);
        
        // 显示成功消息
        this.$message({
          message: '变量添加成功',
          type: 'success',
          duration: 2000
        });
      }
      
      // 关闭变量对话框
      this.variableDialogVisible = false;
    },
    getNodeTypeName(type) {
      const typeMap = {
        'beginNode': '开始',
        'start': '开始',
        'retrieval': '知识检索',
        'generation': '生成回答',
        'dialogue': '对话',
        'classification': '问题分类',
        'message': '静态消息',
        'optimization': '问题优化',
        'keywords': '关键词',
        'conditions': '条件',
        'hub': '集线器',
        'template': '模板转换',
        'loop': '循环',
        'code': '代码',
        'comment': '注释'
      };
      return typeMap[type] || type;
    },
    
    getNodeDescription(type) {
      const descriptionMap = {
        'retrieval': '从知识库中检索相关信息，支持语义搜索和关键词匹配，为回答提供依据。',
        'generation': '根据输入的问题和上下文，生成完整、准确、有帮助的回答内容。',
        'dialogue': '管理多轮对话流程，保持上下文连贯性，实现自然的交互体验。',
        'classification': '对输入的问题进行分类，识别问题意图和类型，引导后续处理流程。',
        'message': '显示预设的静态消息，适用于欢迎语、引导提示或固定回复场景。',
        'optimization': '优化用户输入的问题，纠正拼写错误、补充缺失信息或重新表述以提高理解准确度。',
        'keywords': '提取文本中的关键词和实体，用于信息筛选、分类或标记。',
        'conditions': '根据设定的条件判断分支走向，实现流程的动态控制和决策。',
        'hub': '连接多个节点，集中管理数据流向，简化复杂流程的连接关系。',
        'template': '将内容转换为预定义的模板格式，标准化输出结构。',
        'loop': '重复执行特定操作，直到满足结束条件，用于批量处理或迭代改进。',
        'code': '执行自定义代码片段，处理复杂逻辑或特殊功能，增强系统灵活性。',
        'comment': '添加说明性文字，不参与实际流程执行，用于记录设计思路或操作指南。',
        'start': '工作流的起始节点，标记流程的开始位置。'
      };
      return descriptionMap[type] || '自定义节点';
    },
    
    showAddVariable() {
      // 触发显示添加变量对话框的事件
      this.$emit('show-variable-dialog');
    },
    
    handleEditClassification(data) {
      // 触发编辑分类对话框
      this.$emit('edit-classification', {
        id: this.node.id,
        title: data.title,
        categories: data.categories,
        onSave: data.onSave
      });
    },

    // 处理子组件请求图表数据的事件
    handleRequestGraphData() {
      console.log('[NodeDrawer] 收到请求图表数据事件');
      const data = this.graphData;
      console.log('[NodeDrawer] 返回图表数据:', data);

      // 如果有数据，通过事件总线或其他方式传递给子组件
      if (data) {
        this.$nextTick(() => {
          // 触发子组件的数据更新
          this.$children.forEach(child => {
            if ((child.$options.name === 'ClassificationSettings' || child.$options.name === 'KeywordsSettings') &&
                typeof child.updateGraphData === 'function') {
              child.updateGraphData(data);
            }
          });
        });
      }
    },
    
    updateConditionSettings(data) {
      // 不合并原始数据，直接使用新数据，确保 cases 是一个新的数组副本
      const updatedData = {
        type: 'conditions',
        modelType: this.node.data.modelType || 'conditions',
        modelId: this.node.data.modelId || 'conditions',
        modelName: data.title || this.node.data.modelName || '条件',
        id: this.node.data.id || 'conditions',
        title: data.title || '条件',
        elseAction: data.elseAction,
        cases: Array.isArray(data.cases) ? JSON.parse(JSON.stringify(data.cases)) : []
      };
      
      // 更新条件节点设置
      this.$emit('save', {
        nodeId: this.node.id,
        data: updatedData
      });
      
      // 如果有自定义保存回调，则调用它
      if (typeof this.customSaveCallback === 'function') {
        this.customSaveCallback(updatedData);
      }
    },
    
    saveSettings(data) {
      let processedData = data;

      // 对Generation节点进行特殊处理，将systemPrompt映射为form.prompt
      if (this.node.type === 'generation' && data.systemPrompt !== undefined) {
        console.log('[NodeDrawer] 处理Generation节点保存数据:', data);

        processedData = {
          ...data,
          form: {
            ...this.node.data.form,
            prompt: data.systemPrompt,
            cite: data.enableCitation,
            message_history_window_size: data.historySize,
            llm_id: this.node.data.form?.llm_id || "qwen-max@Tongyi-Qianwen"
          }
        };

        console.log('[NodeDrawer] 处理后的Generation节点数据:', processedData);
      }

      // 对Keywords节点进行特殊处理，确保form数据正确传递
      if ((this.node.type === 'keywords' || this.node.type === 'keywordNode') && data.form) {
        console.log('[NodeDrawer] 处理Keywords节点保存数据:', data);
        console.log('[NodeDrawer] Keywords节点form.query:', data.form.query);

        processedData = {
          ...data,
          data: {
            ...this.node.data,
            name: data.name,
            selectedModel: data.selectedModel,
            topN: data.topN,
            variables: data.variables,
            form: {
              ...this.node.data.form,
              ...data.form
            }
          }
        };

        console.log('[NodeDrawer] 处理后的Keywords节点数据:', processedData);
        console.log('[NodeDrawer] 处理后的form.query:', processedData.data.form.query);
      }

      // 发送保存事件
      this.$emit('save', {
        nodeId: this.node.id,
        data: processedData
      });

      // 关闭抽屉
      this.$emit('close');
    },

    setCustomSaveCallback(callback) {
      this.customSaveCallback = callback;
    },

    onLoopSettingsInput(val) {
      this.saveSettings({ ...val });
    },
    onTemplateSettingsInput(val) {
      this.saveSettings({ form: { ...val } });
    },

    // 保存Begin节点数据
    saveBeginNodeData() {
      // 使用ref访问BeginSettings组件
      const beginSettingsComponent = this.$refs.beginSettings;

      if (beginSettingsComponent && typeof beginSettingsComponent.getFormData === 'function') {
        const formData = beginSettingsComponent.getFormData();
        console.log('[NodeDrawer] 关闭时保存Begin节点数据:', formData);

        // 保存数据
        this.saveSettings(formData);
      }
    }
  }
}
</script>

<style scoped>
.node-drawer-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 0;
}

.drawer-header {
  padding: 16px 24px;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap; /* 允许在小屏幕上换行 */
}

.drawer-header h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
  flex: 0 0 auto; /* 不拉伸标题 */
  margin-right: 10px; /* 在标题和工具栏之间添加间距 */
}

.drawer-body {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

.drawer-footer {
  padding: 16px 24px;
  border-top: 1px solid #e8e8e8;
  text-align: right;
}
</style> 