<template>
  <el-drawer
    :visible.sync="drawerVisible"
    :with-header="true"
    title="AI 对话测试"
    size="40%"
    direction="rtl"
    :before-close="handleClose"
    class="chat-drawer"
  >
    <div class="chat-container">
      <!-- 对话历史区域 -->
      <div class="chat-messages" ref="messagesContainer">
        <div v-if="messages.length === 0" class="empty-chat">
          <i class="el-icon-chat-dot-round"></i>
          <p>开始与AI对话吧！</p>
        </div>
        
        <div 
          v-for="(message, index) in messages" 
          :key="index" 
          class="message-item"
          :class="{ 'user-message': message.role === 'user', 'ai-message': message.role === 'assistant' }"
        >
          <div class="message-avatar">
            <i :class="message.role === 'user' ? 'el-icon-user' : 'el-icon-cpu'"></i>
          </div>
          <div class="message-content">
            <div class="message-text">
              <div v-html="renderMarkdown(message.content)"></div>
              <!-- 模拟流式输出时显示光标 -->
              <span v-if="message.isStreaming" class="streaming-cursor">|</span>
            </div>
            <div class="message-time">{{ formatTime(message.timestamp) }}</div>
          </div>
        </div>
        
        <!-- AI正在输入指示器 -->
        <div v-if="isTyping" class="message-item ai-message typing">
          <div class="message-avatar">
            <i class="el-icon-cpu"></i>
          </div>
          <div class="message-content">
            <div class="typing-indicator">
              <span></span>
              <span></span>
              <span></span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 配置选项区域 -->
      <div class="chat-config-area">
        <el-row :gutter="10" type="flex" align="middle">
          <el-col :span="12">
            <el-tooltip content="启用后将始终通过Agent工作流处理，确保工作流逻辑正确执行" placement="top">
              <el-switch
                v-model="alwaysUseWorkflow"
                active-text="使用工作流"
                inactive-text="混合模式"
                active-color="#13ce66"
                inactive-color="#ff4949"
              ></el-switch>
            </el-tooltip>
          </el-col>
          <el-col :span="12">
            <el-text size="small" type="info">
              {{ alwaysUseWorkflow ? '✅ 通过Agent工作流处理' : '⚠️ 可能绕过工作流' }}
            </el-text>
          </el-col>
        </el-row>
      </div>

      <!-- 输入区域 -->
      <div class="chat-input-area">
        <div class="input-container">
          <el-input
            v-model="inputMessage"
            type="textarea"
            :rows="3"
            placeholder="输入您的问题..."
            @keydown.enter.ctrl="sendMessage"
            :disabled="isTyping"
            resize="none"
          ></el-input>
          <div class="input-actions">
            <el-button
              type="primary"
              size="small"
              @click="sendMessage"
              :loading="isTyping"
              :disabled="!inputMessage.trim()"
            >
              发送 (Ctrl+Enter)
            </el-button>
            <el-button
              size="small"
              @click="clearChat"
              :disabled="messages.length === 0"
            >
              清空
            </el-button>
            <el-dropdown trigger="click" size="small">
              <el-button size="small" type="info">
                调试 <i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item @click.native="logConversationData">
                  <i class="el-icon-view"></i> 查看对话数据
                </el-dropdown-item>
                <el-dropdown-item @click.native="logRetrievalDetails">
                  <i class="el-icon-search"></i> 查看检索详情
                </el-dropdown-item>
                <el-dropdown-item @click.native="exportConversationData">
                  <i class="el-icon-download"></i> 导出对话数据
                </el-dropdown-item>
                <el-dropdown-item @click.native="getSavedConversationData">
                  <i class="el-icon-folder-opened"></i> 获取保存数据
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script>
// import { agentCompletionOpenAI } from '@/api/openai-compatible-API'; // 🔥 注释掉OpenAI兼容API
import {
  converseWithAgentNative,
  createAgentSession
} from '@/api/session-management-API';
// import { retrieveChunks } from '@/api/chunk-management-API'; // 🔥 注释掉混合检索API
// import { extractKeywords as extractKeywordsUtil } from '@/utils/keywordExtractor.js'; // 🔥 注释掉关键词提取

export default {
  name: 'ChatDrawer',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // 当前工作流的Agent ID
    agentId: {
      type: String,
      default: null
    },
    // 当前工作流的DSL数据
    workflowDSL: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      inputMessage: '',
      isTyping: false,
      messages: [],

      // 🔥 原生Agent API会话管理
      currentSessionId: null, // 当前会话ID
      useNativeAPI: true,     // 是否使用原生Agent API（默认启用）

      // 对话保存相关
      lastSaveTime: null,
      saveStatus: 'idle', // idle, saving, saved, error

      // 检索结果缓存
      retrievalResults: [], // 存储每次检索的结果
      currentReference: [],  // 当前对话轮次的引用数据

      // 混合检索配置
      useHybridRetrieval: false, // 是否使用混合检索模式（绕过工作流）
      alwaysUseWorkflow: true    // 是否始终使用Agent工作流
    };
  },
  computed: {
    drawerVisible: {
      get() {
        return this.visible;
      },
      set(value) {
        if (!value) {
          this.$emit('close');
        }
      }
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        console.log('[聊天API调试] ChatDrawer打开');
        console.log('[聊天API调试] 接收到的Agent ID:', this.agentId);
        console.log('[聊天API调试] 接收到的工作流DSL:', this.workflowDSL);

        // 初始化欢迎消息
        this.initializeChat();

        this.$nextTick(() => {
          this.scrollToBottom();
        });
      }
    },
    agentId(newVal) {
      console.log('[聊天API调试] Agent ID变化:', newVal);
    }
  },

  methods: {
    handleClose() {
      this.$emit('close');
    },

    // 初始化聊天
    async initializeChat() {
      // 清空之前的消息
      this.messages = [];

      // 🔥 重置会话ID，下次对话将创建新会话
      this.currentSessionId = null;
      console.log('[会话管理] 初始化聊天，会话ID已重置');

      // 检查是否有Agent ID
      if (!this.agentId) {
        this.messages.push({
          role: 'assistant',
          content: '当前工作流未保存为Agent，请先保存工作流后再进行对话测试。',
          timestamp: new Date()
        });
        return;
      }

      // 🔥 自动调用一次原生Agent API获取真正的开场白和session_id
      try {
        console.log('[初始化] 自动调用Agent API获取开场白...');
        await this.initializeAgentSession();
      } catch (error) {
        console.error('[初始化] 获取开场白失败:', error);
        // 如果获取开场白失败，显示默认消息
        const fallbackMessage = this.getWelcomeMessageFromDSL();
        this.messages.push({
          role: 'assistant',
          content: fallbackMessage || 'Hi! I\'m your smart assistant. What can I do for you?',
          timestamp: new Date()
        });
      }
    },

    // 🔥 初始化Agent会话，先创建会话获取开场白和session_id
    async initializeAgentSession() {
      try {
        // 🔥 第一步：创建Agent会话获取开场白
        console.log('[初始化] 创建新的Agent会话...');

        const sessionData = {
          user_id: this.userId || undefined
        };

        // 使用Agent的会话创建API
        const sessionResponse = await createAgentSession(this.agentId, sessionData);

        if (sessionResponse && sessionResponse.data && sessionResponse.data.code === 0) {
          const sessionInfo = sessionResponse.data.data;

          // 保存session_id
          this.sessionId = sessionInfo.id;
          console.log('[初始化] Agent会话创建成功，session_id:', this.sessionId);

          // Agent会话创建通常不会直接返回开场白，需要发送空问题获取
          console.log('[初始化] Agent会话创建成功，发送空问题获取开场白...');
          await this.initializeWithAgentAPI();
          return;
        }

        // 如果会话创建失败，尝试使用Agent API初始化
        console.log('[初始化] Agent会话创建失败，尝试使用Agent API初始化...');
        await this.initializeWithAgentAPI();

      } catch (error) {
        console.error('[初始化] Agent会话初始化失败:', error);
        throw error;
      }
    },



    // 🔥 使用Agent API初始化（备用方案）
    async initializeWithAgentAPI() {
      try {
        // 发送初始化请求来创建会话并获取开场白
        const requestData = {
          question: '', // 🔥 提供空的question参数来初始化会话
          sync_dsl: true, // 同步最新DSL
          stream: false
        };

        // 检查Begin组件是否需要参数
        const beginParams = this.getBeginComponentParams();
        if (beginParams && Object.keys(beginParams).length > 0) {
          // 如果Begin组件需要参数，传递这些参数
          Object.assign(requestData, beginParams);
          console.log('[初始化] Begin组件需要参数:', beginParams);
        } else {
          // 如果Begin组件不需要参数，使用空question来创建会话
          console.log('[初始化] Begin组件不需要参数，使用空question创建新会话');
        }

        console.log('[初始化] 发送Agent API初始化请求:', requestData);

        // 调用原生Agent API
        const response = await converseWithAgentNative(this.agentId, requestData);

        console.log('[初始化] 收到Agent API响应:', response);

        // 处理响应
        if (response.data && response.data.data) {
          const responseData = response.data.data;

          // 保存session_id
          if (responseData.session_id) {
            this.currentSessionId = responseData.session_id;
            console.log('[初始化] 获取到session_id:', this.currentSessionId);
          }

          // 显示开场白（如果有）
          if (responseData.answer && responseData.answer.trim()) {
            this.messages.push({
              role: 'assistant',
              content: responseData.answer,
              timestamp: new Date(),
              sessionId: responseData.session_id,
              reference: responseData.reference
            });
            console.log('[初始化] 显示开场白:', responseData.answer);
          } else {
            // 如果没有开场白，显示默认消息
            const fallbackMessage = this.getWelcomeMessageFromDSL();
            this.messages.push({
              role: 'assistant',
              content: fallbackMessage || 'Hi! I\'m your smart assistant. What can I do for you?',
              timestamp: new Date()
            });
          }
        }

      } catch (error) {
        console.error('[初始化] Agent会话初始化失败:', error);
        throw error;
      }
    },

    // 从DSL中获取Begin节点的开场白
    getWelcomeMessageFromDSL() {
      try {
        // 检查DSL数据是否存在
        if (!this.workflowDSL || !this.workflowDSL.components) {
          console.log('[聊天API调试] DSL数据不存在，使用默认开场白');
          return "Hi! I'm your smart assistant. What can I do for you?";
        }

        // 查找Begin节点
        const beginComponent = this.workflowDSL.components.begin ||
                              this.workflowDSL.components['begin'] ||
                              Object.values(this.workflowDSL.components).find(comp =>
                                comp.obj && comp.obj.component_name === 'Begin'
                              );

        if (beginComponent && beginComponent.obj) {
          // 优先从output.content中获取
          if (beginComponent.obj.output && beginComponent.obj.output.content) {
            if (typeof beginComponent.obj.output.content === 'string') {
              console.log('[聊天API调试] 从Begin节点output.content获取开场白:', beginComponent.obj.output.content);
              return beginComponent.obj.output.content;
            } else if (beginComponent.obj.output.content['0'] && beginComponent.obj.output.content['0'].content) {
              console.log('[聊天API调试] 从Begin节点output.content.0.content获取开场白:', beginComponent.obj.output.content['0'].content);
              return beginComponent.obj.output.content['0'].content;
            }
          }

          // 其次从params.prologue中获取
          if (beginComponent.obj.params && beginComponent.obj.params.prologue) {
            console.log('[聊天API调试] 从Begin节点params.prologue获取开场白:', beginComponent.obj.params.prologue);
            return beginComponent.obj.params.prologue;
          }
        }

        console.log('[聊天API调试] 未找到Begin节点或开场白内容，使用默认开场白');
        return "Hi! I'm your smart assistant. What can I do for you?";
      } catch (error) {
        console.error('[聊天API调试] 获取开场白时出错:', error);
        return "Hi! I'm your smart assistant. What can I do for you?";
      }
    },

    // 🔥 获取Begin组件的参数（用于初始化会话）
    getBeginComponentParams() {
      try {
        if (!this.workflowDSL || !this.workflowDSL.components) {
          return {};
        }

        // 查找Begin节点
        const beginComponent = this.workflowDSL.components.begin ||
                              this.workflowDSL.components['begin'] ||
                              Object.values(this.workflowDSL.components).find(comp =>
                                comp.obj && comp.obj.component_name === 'Begin'
                              );

        if (beginComponent && beginComponent.obj && beginComponent.obj.params) {
          const params = { ...beginComponent.obj.params };

          // 移除prologue，因为它不是API参数，是内部配置
          delete params.prologue;

          console.log('[DSL解析] Begin组件参数:', params);
          return params;
        }

        return {};
      } catch (error) {
        console.error('[DSL解析] 获取Begin组件参数失败:', error);
        return {};
      }
    },

    async sendMessage() {
      if (!this.inputMessage.trim() || this.isTyping) return;

      // 清空当前引用数据，开始新的对话轮次
      this.clearCurrentReference();

      const userMessage = {
        role: 'user',
        content: this.inputMessage.trim(),
        timestamp: new Date()
      };

      console.log('[聊天API调试] 发送用户消息:', userMessage.content);
      console.log('[聊天API调试] 当前Agent ID:', this.agentId);
      
      this.messages.push(userMessage);
      this.inputMessage = '';
      this.isTyping = true;
      
      this.$nextTick(() => {
        this.scrollToBottom();
      });
      
      try {
        // 🔥 无论什么情况都使用纯原生Agent API发送消息
        console.log('[聊天API调试] 使用纯原生Agent API发送消息');
        await this.sendMessageToAgentNative(userMessage.content);
      } catch (error) {
        console.error('[聊天API调试] 发送消息失败:', error);
        this.handleApiError(error);
      } finally {
        // 确保isTyping状态被重置（防止出错时状态卡住）
        if (this.isTyping) {
          this.isTyping = false;
        }
        this.$nextTick(() => {
          this.scrollToBottom();
        });

        // 保存对话数据
        this.saveConversationData();
      }
    },
    
    // � 已删除sendMessageToAgent方法，只保留纯原生Agent API

    // 🔥 纯原生Agent API方法（处理所有情况）
    async sendMessageToAgentNative(userInput) {
      try {
        console.log('[原生Agent API] 开始调用纯原生Agent API');
        console.log('[原生Agent API] 用户输入:', userInput);
        console.log('[原生Agent API] 当前会话ID:', this.currentSessionId);

        // 🔥 内部检查逻辑
        // 检查是否有agentId
        if (!this.agentId) {
          throw new Error('当前工作流未保存为Agent，请先保存为Agent后再进行对话测试');
        }

        // 检查工作流是否包含Answer组件
        const hasAnswerComponent = this.checkForAnswerComponent();
        if (!hasAnswerComponent) {
          throw new Error('工作流中缺少Answer组件，请添加Answer组件后再保存并测试对话');
        }

        // 🔥 使用原生Agent API替代OpenAI兼容API
        const requestData = {
          question: userInput,
          stream: false,
          sync_dsl: true, // 🔥 关键：同步最新DSL
        };

        // 如果有现有会话，添加session_id
        if (this.currentSessionId) {
          requestData.session_id = this.currentSessionId;
        }

        console.log('[原生Agent API] 发送请求数据:', requestData);

        // 🔥 调用原生Agent API
        const response = await converseWithAgentNative(this.agentId, requestData);

        console.log('[原生Agent API] 收到响应:', response);

        // 🔥 如果出现组件输入推断错误，输出详细的DSL调试信息
        if (response.data && response.data.code === 102) {
          console.error('🚨 [DSL调试] 出现组件输入推断错误，开始调试...');
          console.error('🚨 [DSL调试] 错误详情:', response.data);
          console.error('🚨 [DSL调试] 当前使用的API:', {
            agentId: this.agentId,
            endpoint: `/api/v1/agents/${this.agentId}/completions`,
            requestData: requestData
          });

          // 获取当前工作流DSL
          const currentDSL = this.$parent.$refs.x6Graph?.getGraphData();
          if (currentDSL) {
            console.log('📋 [DSL调试] 当前图表数据:', currentDSL);

            // 检查分类节点
            const categorizeNodes = currentDSL.nodes?.filter(node =>
              node.type === 'categorizeNode' ||
              (node.data && (node.data.label === 'Categorize' || node.data.name === '问题分类'))
            );

            if (categorizeNodes && categorizeNodes.length > 0) {
              categorizeNodes.forEach(node => {
                console.log(`🔍 [DSL调试] 分类节点 ${node.id}:`, {
                  type: node.type,
                  data: node.data,
                  category_description: node.data?.form?.category_description
                });
              });
            } else {
              console.log('❌ [DSL调试] 未找到分类节点');
            }

            // 🔥 获取并显示完整的DSL结构
            console.log('🔧 [DSL调试] 开始构建完整DSL...');
            try {
              const fullDSL = this.$parent.getCurrentWorkflowDSL();
              console.log('📄 [DSL调试] 完整DSL结构:', fullDSL);

              // 特别检查components中的分类组件
              if (fullDSL && fullDSL.components) {
                Object.keys(fullDSL.components).forEach(componentId => {
                  const component = fullDSL.components[componentId];
                  if (component.obj && component.obj.component_name === 'Categorize') {
                    console.log(`🎯 [DSL调试] Components中的分类组件 ${componentId}:`, {
                      component_name: component.obj.component_name,
                      params: component.obj.params,
                      upstream: component.upstream,
                      downstream: component.downstream
                    });
                  }
                });
              }
            } catch (dslError) {
              console.error('❌ [DSL调试] 构建DSL时出错:', dslError);
            }
          }
        }

        // 处理返回
        return this.handleApiResponse(response);

      } catch (error) {
        console.error('[原生Agent API] 调用失败:', error);

        // 立即关闭"正在输入"指示器
        this.isTyping = false;

        // 如果有正在流式输出的消息，移除它
        const lastMessage = this.messages[this.messages.length - 1];
        if (lastMessage && lastMessage.isStreaming) {
          this.messages.pop();
        }

        this.handleApiError(error);
        throw error;
      }
    },

    // 统一处理API响应
    async handleApiResponse(response) {
      try {
        let fullContent = '';
        let sessionId = null;
        let reference = null;

        // 🔥 处理原生Agent API响应格式
        if (response.data && response.data.data) {
          const responseData = response.data.data;

          // 正常对话响应
          if (typeof responseData === 'object' && responseData.answer !== undefined) {
            fullContent = responseData.answer || '';
            sessionId = responseData.session_id;
            reference = responseData.reference;

            // 🔥 保存会话ID用于后续对话
            if (sessionId && !this.currentSessionId) {
              this.currentSessionId = sessionId;
              console.log('[会话管理] 保存新会话ID:', sessionId);
            }

            console.log('[聊天API调试] 原生Agent API响应解析:', {
              answer: fullContent,
              sessionId,
              hasReference: !!reference
            });

          } else if (responseData === true) {
            // 流式输出结束标志，忽略
            console.log('[聊天API调试] 收到流式输出结束标志，忽略');
            return;
          } else {
            throw new Error('原生Agent API响应格式错误：data.data字段格式不正确');
          }
        }
        // 兼容OpenAI格式（向后兼容）
        else if (response.data && response.data.choices && response.data.choices[0]?.message?.content) {
          fullContent = response.data.choices[0].message.content;
          console.log('[聊天API调试] 使用OpenAI兼容格式解析');
        }
        else {
          console.error('[聊天API调试] API返回数据格式错误，完整响应:', response);
          throw new Error(`API返回数据格式错误: ${JSON.stringify(response.data)}`);
        }



        // 立即关闭"正在输入"指示器
        this.isTyping = false;

        // 创建一个空的AI消息用于模拟流式输出
        const aiMessage = {
          role: 'assistant',
          content: '',
          timestamp: new Date(),
          isStreaming: true,
          sessionId: sessionId, // 保存会话ID
          reference: reference   // 保存引用信息
        };
        this.messages.push(aiMessage);

        console.log('[聊天API调试] 开始模拟流式输出:', fullContent);

        // 模拟流式输出效果
        await this.simulateStreamingOutput(aiMessage, fullContent);

      } catch (error) {
        console.error('[聊天API调试] 响应处理失败:', error);

        // 立即关闭"正在输入"指示器
        this.isTyping = false;

        // 如果有正在流式输出的消息，移除它
        const lastMessage = this.messages[this.messages.length - 1];
        if (lastMessage && lastMessage.isStreaming) {
          this.messages.pop();
        }

        this.handleApiError(error);
        throw error;
      }
    },

    // 模拟流式输出效果
    async simulateStreamingOutput(aiMessage, fullContent) {
      // 按字符分割，但在标点符号处稍作停顿
      const chars = fullContent.split('');

      for (let i = 0; i < chars.length; i++) {
        // 添加字符到消息内容
        aiMessage.content += chars[i];

        // 实时滚动到底部
        this.$nextTick(() => {
          this.scrollToBottom();
        });

        // 根据字符类型设置不同的延迟
        let delay = 20; // 基础延迟
        const char = chars[i];

        if (char === '。' || char === '！' || char === '？' || char === '.' || char === '!' || char === '?') {
          delay = 200; // 句号等停顿较长
        } else if (char === '，' || char === '；' || char === '：' || char === ',' || char === ';' || char === ':') {
          delay = 100; // 逗号等停顿中等
        } else if (char === ' ') {
          delay = 30; // 空格稍作停顿
        } else if (/[\u4e00-\u9fa5]/.test(char)) {
          delay = 25; // 中文字符
        } else {
          delay = 15; // 英文字符较快
        }

        // 延迟一段时间再显示下一个字符
        await new Promise(resolve => setTimeout(resolve, delay));
      }

      // 输出完成，移除流式状态
      aiMessage.isStreaming = false;

      console.log('[聊天API调试] 模拟流式输出完成');

      // 最后滚动到底部
      this.$nextTick(() => {
        this.scrollToBottom();
      });

      // 保存对话数据
      this.saveConversationData();
    },

    // 检查工作流中是否包含Retrieval组件
    checkForRetrievalComponent() {
      try {
        if (!this.workflowDSL || !this.workflowDSL.components) {
          console.log('[聊天API调试] 没有workflowDSL或components');
          return false;
        }

        console.log('[聊天API调试] 检查工作流组件:', Object.keys(this.workflowDSL.components));

        // 检查是否有Retrieval类型的组件
        const retrievalComponents = Object.values(this.workflowDSL.components).filter(component =>
          component.obj?.component_name === 'Retrieval'
        );

        console.log('[聊天API调试] 找到的Retrieval组件:', retrievalComponents.length);

        if (retrievalComponents.length > 0) {
          console.log('[聊天API调试] Retrieval组件详情:', retrievalComponents[0]);
        }

        const hasRetrieval = retrievalComponents.length > 0;
        console.log('[聊天API调试] Retrieval组件检查结果:', hasRetrieval);
        return hasRetrieval;
      } catch (error) {
        console.error('[聊天API调试] 检查Retrieval组件时出错:', error);
        return false;
      }
    },



    // 🔥 已删除所有生成方法，从generatePreSalesAnswer到formatDocumentContent



    // 🔥 已删除sendNormalMessage方法

    // 检查工作流是否包含Answer组件
    checkForAnswerComponent() {
      try {
        // 通过父组件获取图表数据
        const graphData = this.$parent.$refs.x6Graph?.getGraphData();
        if (!graphData || !graphData.nodes) {
          console.log('[聊天API调试] 无法获取图表数据');
          return false;
        }

        // 检查是否有Answer类型的节点
        const hasAnswer = graphData.nodes.some(node => {
          const nodeType = node.type || node.data?.type || node.data?.modelType;
          return nodeType === 'answerNode' ||
                 nodeType === 'answer' ||
                 nodeType === 'Answer' ||
                 (node.data && (
                   node.data.label === 'Answer' ||
                   node.data.name === 'Answer' ||
                   node.data.modelName === 'Answer'
                 ));
        });

        console.log('[聊天API调试] Answer组件检查结果:', {
          hasAnswer,
          totalNodes: graphData.nodes.length,
          nodeTypes: graphData.nodes.map(n => n.type || n.data?.type || n.data?.modelType)
        });

        return hasAnswer;
      } catch (error) {
        console.error('[聊天API调试] 检查Answer组件时出错:', error);
        return false;
      }
    },

    handleApiError(error) {
      console.log('[聊天API调试] 处理API错误:', error);

      let errorContent = '抱歉，发生了错误：';

      // 根据错误类型提供更具体的错误信息
      if (error.message.includes('Agent ID未设置') || error.message.includes('未保存')) {
        errorContent = '请先保存当前工作流为Agent，然后再进行对话测试。';
      } else if (error.message.includes('Answer组件')) {
        errorContent = '工作流中缺少Answer组件。请从左侧组件面板拖拽一个Answer组件到画布上，然后保存工作流后再进行对话测试。';
      } else if (error.message.includes('There have to be an \'Answer\' component')) {
        errorContent = '系统检测到工作流中缺少必需的Answer组件。请添加Answer组件后重新保存工作流。';
      } else if (error.message.includes('API返回数据格式错误')) {
        errorContent = 'Agent响应格式异常，请检查Agent配置是否正确。';
      } else if (error.message.includes('timeout') || error.code === 'ECONNABORTED') {
        errorContent = 'Agent响应超时，可能是因为处理时间较长。请稍后重试或简化您的问题。';
      } else if (error.response && error.response.status === 404) {
        errorContent = 'Agent不存在，请确认Agent ID是否正确。';
      } else if (error.response && error.response.status === 401) {
        errorContent = 'API认证失败，请检查API密钥配置。';
      } else if (error.message.includes('网络')) {
        errorContent = '网络连接失败，请检查网络连接后重试。';
      } else {
        errorContent = `${error.message || '未知错误，请稍后重试'}`;
      }

      const errorMessage = {
        role: 'assistant',
        content: errorContent,
        timestamp: new Date()
      };
      this.messages.push(errorMessage);
    },
    
    clearChat() {
      this.$confirm('确定要清空所有对话记录吗？', '确认清空', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 重新初始化聊天
        this.initializeChat();
      }).catch(() => {
        // 用户取消
      });
    },
    
    scrollToBottom() {
      const container = this.$refs.messagesContainer;
      if (container) {
        container.scrollTop = container.scrollHeight;
      }
    },
    
    formatTime(timestamp) {
      return new Date(timestamp).toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
      });
    },

    // 简单的Markdown渲染器
    renderMarkdown(content) {
      if (!content) return '';

      return content
        // 处理加粗文本 **text** -> <strong>text</strong>
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        // 处理换行符 -> <br>
        .replace(/\n/g, '<br>')
        // 处理段落分隔（双换行）-> <br><br>
        .replace(/<br><br>/g, '<br><br>');
    },

    // 保存对话数据到工作流状态
    saveConversationData() {
      try {
        this.saveStatus = 'saving';
        console.log('[对话保存] 开始保存对话数据');

        // 获取当前工作流数据
        const graphData = this.$parent.$refs.x6Graph?.getGraphData();
        if (!graphData) {
          console.warn('[对话保存] 无法获取工作流数据');
          this.saveStatus = 'error';
          return;
        }

        // 构建对话历史数据
        const conversationData = this.buildConversationData();

        // 更新工作流的对话相关数据
        this.updateWorkflowConversationData(graphData, conversationData);

        // 触发工作流保存
        this.$emit('conversation-updated', conversationData);

        // 更新保存状态
        this.saveStatus = 'saved';
        this.lastSaveTime = new Date().toISOString();

        console.log('[对话保存] 对话数据保存完成');

        // 显示保存成功提示（可选）
        this.$message({
          message: '对话数据已保存',
          type: 'success',
          duration: 1000,
          showClose: false
        });

      } catch (error) {
        console.error('[对话保存] 保存对话数据时出错:', error);
        this.saveStatus = 'error';

        this.$message({
          message: '保存对话数据失败',
          type: 'error',
          duration: 2000
        });
      }
    },

    // 构建对话数据
    buildConversationData() {
      console.log('[对话保存] 构建对话数据');

      // 构建history格式（简化版）
      const history = this.messages.map(msg => [
        msg.role,
        msg.content
      ]);

      // 构建messages格式（完整版）
      const messages = this.messages.map(msg => ({
        content: msg.content,
        id: msg.id || this.generateMessageId(),
        role: msg.role,
        timestamp: msg.timestamp || new Date().toISOString()
      }));

      // 构建执行路径
      const path = this.buildExecutionPath();

      // 构建引用信息
      const reference = this.buildReferenceData();

      const conversationData = {
        history,
        messages,
        path,
        reference,
        rerank: false,
        embed_id: "text-embedding-v2@Tongyi-Qianwen"
      };

      console.log('[对话保存] 构建的对话数据:', conversationData);
      return conversationData;
    },

    // 构建执行路径
    buildExecutionPath() {
      const path = [];

      // 添加初始路径
      if (this.messages.length > 0) {
        path.push(["begin"]);
      }

      // 为每个用户消息添加执行路径
      this.messages.forEach((msg, index) => {
        if (msg.role === 'user' && index > 0) {
          // 构建标准的执行路径
          const executionPath = this.buildStandardExecutionPath(msg);
          if (executionPath.length > 0) {
            path.push(executionPath);
          }
        }
      });

      return path;
    },

    // 判断是否为追问
    isFollowUpQuestion(content) {
      // 如果没有对话历史，肯定不是追问
      if (!this.conversationHistory || this.conversationHistory.length <= 1) {
        return false;
      }

      // 简单的追问判断逻辑
      const followUpKeywords = [
        '还有', '另外', '此外', '补充', '详细', '具体', '进一步', '更多',
        '那么', '那', '这样', '这个', '那个', '它', '他', '她',
        '继续', '接着', '然后', '再', '还', '也', '同样',
        '什么', '怎么', '为什么', '如何', '哪里', '哪个', '多少'
      ];

      // 检查是否包含追问关键词
      const hasFollowUpKeywords = followUpKeywords.some(keyword =>
        content.includes(keyword)
      );

      // 检查输入长度（追问通常比较短）
      const isShort = content.length < 20;

      // 检查是否有代词或指代词
      const hasPronouns = /这|那|它|他|她/.test(content);

      return hasFollowUpKeywords || (isShort && hasPronouns);
    },

    // 构建标准执行路径
    buildStandardExecutionPath(userMessage) {
      // 检查是否为追问
      const isFollowUp = this.isFollowUpQuestion(userMessage.content);

      if (isFollowUp) {
        // 追问的完整路径
        return [
          "RewriteQuestion_0",
          "KeywordExtract_0",
          "Categorize_0",
          this.determineRetrievalPath(userMessage.content),
          this.determineGeneratePath(userMessage.content),
          "Answer_0"
        ];
      } else {
        // 新问题的路径
        return [
          "KeywordExtract_0",
          "Categorize_0",
          this.determineRetrievalPath(userMessage.content),
          this.determineGeneratePath(userMessage.content),
          "Answer_0"
        ];
      }
    },

    // 确定检索路径
    determineRetrievalPath(content) {
      // 简单的分类逻辑
      if (content.includes('售前') || content.includes('之前')) {
        return "Retrieval_0";
      } else if (content.includes('售后') || content.includes('之后')) {
        return "Retrieval_1";
      }
      return "Retrieval_0"; // 默认
    },

    // 确定生成路径
    determineGeneratePath(content) {
      // 对应检索路径的生成路径
      if (content.includes('售前') || content.includes('之前')) {
        return "Generate_0";
      } else if (content.includes('售后') || content.includes('之后')) {
        return "Generate_1";
      }
      return "Generate_0"; // 默认
    },

    // 保存检索结果
    saveRetrievalResults(chunks, keywords) {
      console.log('[检索结果保存] 保存检索结果:', chunks.length, '个片段');

      if (!chunks || chunks.length === 0) {
        return;
      }

      // 构建RAGFlow标准的reference格式
      const referenceData = this.buildReferenceFromChunks(chunks);

      // 保存到当前引用数据
      this.currentReference = referenceData;

      // 添加到检索结果历史
      this.retrievalResults.push({
        timestamp: new Date().toISOString(),
        keywords: keywords,
        chunks: chunks,
        reference: referenceData
      });

      console.log('[检索结果保存] 当前引用数据:', this.currentReference);
    },

    // 从chunks构建RAGFlow标准的reference格式
    buildReferenceFromChunks(chunks) {
      console.log('[引用构建] 开始构建引用数据');

      // 按文档分组chunks
      const docGroups = {};

      chunks.forEach(chunk => {
        const docId = chunk.document_id || chunk.doc_id;
        if (!docGroups[docId]) {
          docGroups[docId] = {
            chunks: [],
            doc_name: chunk.document_name || chunk.doc_name || 'Unknown Document'
          };
        }
        docGroups[docId].chunks.push(chunk);
      });

      // 构建reference数组
      const reference = Object.entries(docGroups).map(([docId, group]) => ({
        chunks: group.chunks.map(chunk => ({
          content: chunk.content || chunk.text || '',
          dataset_id: chunk.dataset_id || '',
          doc_type: chunk.doc_type || '',
          document_id: chunk.document_id || chunk.doc_id || docId,
          document_name: chunk.document_name || chunk.doc_name || group.doc_name,
          id: chunk.id || chunk.chunk_id || '',
          image_id: chunk.image_id || '',
          positions: chunk.positions || [],
          similarity: chunk.similarity || 0,
          term_similarity: chunk.term_similarity || 0,
          url: chunk.url || null,
          vector_similarity: chunk.vector_similarity || 0
        })),
        doc_aggs: [{
          doc_id: docId,
          doc_name: group.doc_name
        }]
      }));

      console.log('[引用构建] 构建完成，共', reference.length, '个文档组');
      return reference;
    },

    // 构建引用数据
    buildReferenceData() {
      // 返回当前对话轮次的引用数据
      console.log('[引用数据] 获取当前引用数据:', this.currentReference.length, '个引用');
      return this.currentReference || [];
    },

    // 清空当前引用数据（开始新的对话轮次时调用）
    clearCurrentReference() {
      console.log('[引用数据] 清空当前引用数据');
      this.currentReference = [];
    },

    // 更新工作流的对话数据
    updateWorkflowConversationData(graphData, conversationData) {
      console.log('[对话保存] 更新工作流对话数据');

      // 更新工作流的全局数据
      if (!graphData.conversationData) {
        graphData.conversationData = {};
      }

      // 保存对话数据
      graphData.conversationData = {
        ...graphData.conversationData,
        ...conversationData,
        lastUpdated: new Date().toISOString()
      };

      // 更新组件的输出数据
      this.updateComponentOutputs(graphData, conversationData);
    },

    // 更新组件输出数据
    updateComponentOutputs(graphData, conversationData) {
      if (!graphData.nodes) return;

      graphData.nodes.forEach(node => {
        if (!node.data) return;

        // 更新Answer组件的输出
        if (node.data.label === 'Answer' || node.id === 'Answer_0') {
          if (!node.data.output) {
            node.data.output = {};
          }

          // 保存最新的对话内容和引用数据
          const lastMessage = this.messages[this.messages.length - 1];
          if (lastMessage && lastMessage.role === 'assistant') {
            node.data.output.content = lastMessage.content;
            node.data.output.reference = conversationData.reference || [];

            // 如果有引用数据，也保存到组件的inputs中
            if (conversationData.reference && conversationData.reference.length > 0) {
              if (!node.data.inputs) {
                node.data.inputs = [];
              }
              // 更新或添加引用输入
              const referenceInput = {
                component_id: 'Retrieval',
                content: conversationData.reference,
                type: 'reference'
              };
              node.data.inputs = node.data.inputs.filter(input => input.type !== 'reference');
              node.data.inputs.push(referenceInput);
            }
          }
        }

        // 🔥 删除KeywordExtract组件的output更新逻辑
        // 让RAGFlow在执行时动态生成KeywordExtract的输出
        // if (node.data.label === 'KeywordExtract' || node.id === 'KeywordExtract_0') {
        //   // 已删除：不再预设KeywordExtract的输出内容
        // }
      });
    },

    // 生成消息ID
    generateMessageId() {
      return Math.random().toString(36).substring(2, 11) + Date.now().toString(36);
    },

    // 获取保存的对话数据
    getSavedConversationData() {
      try {
        const graphData = this.$parent.$refs.x6Graph?.getGraphData();
        if (graphData && graphData.conversationData) {
          console.log('[对话保存] 获取保存的对话数据:', graphData.conversationData);
          return graphData.conversationData;
        }
        return null;
      } catch (error) {
        console.error('[对话保存] 获取保存数据时出错:', error);
        return null;
      }
    },

    // 导出对话数据（用于调试）
    exportConversationData() {
      const conversationData = this.buildConversationData();
      const dataStr = JSON.stringify(conversationData, null, 2);

      // 创建下载链接
      const blob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `conversation_${new Date().getTime()}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      console.log('[对话保存] 对话数据已导出');
    },

    // 在控制台显示对话数据（用于调试）
    logConversationData() {
      const conversationData = this.buildConversationData();
      console.log('=== 当前对话数据 ===');
      console.log('History:', conversationData.history);
      console.log('Messages:', conversationData.messages);
      console.log('Path:', conversationData.path);
      console.log('Reference:', conversationData.reference);
      console.log('=== 检索结果历史 ===');
      console.log('检索次数:', this.retrievalResults.length);
      this.retrievalResults.forEach((result, index) => {
        console.log(`检索 ${index + 1}:`, {
          时间: result.timestamp,
          关键词: result.keywords,
          文档数: result.chunks.length,
          引用数: result.reference.length
        });
      });
      console.log('=== 当前引用数据 ===');
      console.log('当前引用:', this.currentReference);
      console.log('=== 完整数据 ===');
      console.log(JSON.stringify(conversationData, null, 2));
    },

    // 显示检索结果详情
    logRetrievalDetails() {
      console.log('=== 检索结果详情 ===');
      if (this.retrievalResults.length === 0) {
        console.log('暂无检索结果');
        return;
      }

      this.retrievalResults.forEach((result, index) => {
        console.log(`\n--- 检索 ${index + 1} ---`);
        console.log('时间:', result.timestamp);
        console.log('关键词:', result.keywords);
        console.log('检索到的文档片段:');

        result.chunks.forEach((chunk, chunkIndex) => {
          console.log(`  片段 ${chunkIndex + 1}:`);
          console.log(`    文档: ${chunk.document_name || chunk.doc_name}`);
          console.log(`    相似度: ${chunk.similarity || 0}`);
          console.log(`    内容: ${(chunk.content || chunk.text || '').substring(0, 100)}...`);
        });

        console.log('构建的引用数据:');
        result.reference.forEach((ref, refIndex) => {
          console.log(`  引用 ${refIndex + 1}:`);
          console.log(`    文档组: ${ref.doc_aggs?.[0]?.doc_name}`);
          console.log(`    片段数: ${ref.chunks?.length || 0}`);
        });
      });
    },

    // 获取当前处理模式描述
    getCurrentProcessingMode() {
      const hasRetrievalComponent = this.checkForRetrievalComponent();

      if (!hasRetrievalComponent) {
        return '普通模式 - 直接通过Agent工作流处理';
      }

      if (this.alwaysUseWorkflow) {
        return 'Agent工作流模式 - 包含检索组件，通过完整工作流处理';
      } else {
        return '混合检索模式 - 可能绕过工作流直接处理';
      }
    }
  }
};
</script>

<style scoped>
.chat-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background-color: #f8f9fa;
}

.empty-chat {
  text-align: center;
  color: #909399;
  margin-top: 100px;
}

.empty-chat i {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
}

.message-item {
  display: flex;
  margin-bottom: 20px;
  animation: fadeIn 0.3s ease-in;
}

.user-message {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: white;
  flex-shrink: 0;
}

.user-message .message-avatar {
  background-color: #409EFF;
  margin-left: 12px;
}

.ai-message .message-avatar {
  background-color: #67C23A;
  margin-right: 12px;
}

.message-content {
  max-width: 70%;
  min-width: 100px;
}

.message-text {
  background-color: white;
  padding: 12px 16px;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  line-height: 1.5;
  word-wrap: break-word;
}

.user-message .message-text {
  background-color: #409EFF;
  color: white;
}

.message-time {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  text-align: right;
}

.user-message .message-time {
  text-align: left;
}

/* 模拟流式输出光标样式 */
.streaming-cursor {
  display: inline-block;
  color: #409EFF;
  font-weight: bold;
  animation: blink 1s infinite;
  margin-left: 2px;
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

.typing-indicator {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #909399;
  margin-right: 4px;
  animation: typing 1.4s infinite;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
  margin-right: 0;
}

.chat-config-area {
  border-top: 1px solid #EBEEF5;
  border-bottom: 1px solid #EBEEF5;
  padding: 15px 20px;
  background-color: #f8f9fa;
}

.chat-input-area {
  padding: 20px;
  background-color: white;
}

.input-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.input-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-10px);
  }
}

/* 自定义滚动条 */
.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>