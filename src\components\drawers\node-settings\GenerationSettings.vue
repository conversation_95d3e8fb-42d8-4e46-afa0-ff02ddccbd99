<template>
  <div class="generation-settings">
    <div class="form-section">
      <h4>节点标题</h4>
      <el-input 
        v-model="nodeData.name" 
        placeholder="节点标题" 
        style="margin-bottom: 15px"
      ></el-input>
    </div>
    
    <div class="form-section">
      <h4>模型</h4>
      <el-select 
        v-model="nodeData.selectedModel" 
        placeholder="选择模型" 
        style="width: 100%"
        @change="handleModelChange"
      >
        <el-option label="默认大模型" value="默认大模型"></el-option>
        <el-option label="GPT-4" value="GPT-4"></el-option>
        <el-option label="Claude 3" value="Claude 3"></el-option>
        <el-option label="Llama 3" value="Llama 3"></el-option>
        <el-option label="Gemini" value="Gemini"></el-option>
      </el-select>
    </div>
    
    <div class="form-section">
      <h4><span class="required-marker">*</span> 系统提示词</h4>
      <el-input
        type="textarea"
        v-model="nodeData.systemPrompt"
        :rows="5"
        placeholder="请输入系统提示词..."
      ></el-input>
    </div>
    
    <div class="form-section">
      <h4>可用的工具</h4>
      <el-select 
        v-model="nodeData.tools" 
        multiple
        collapse-tags
        placeholder="请选择工具" 
        style="width: 100%"
      >
        <el-option label="Web 搜索" value="web_search"></el-option>
        <el-option label="文件阅读" value="file_reader"></el-option>
        <el-option label="代码执行" value="code_executor"></el-option>
        <el-option label="图像生成" value="image_generator"></el-option>
      </el-select>
    </div>
    
    <div class="form-section">
      <h4>引用</h4>
      <el-switch
        v-model="nodeData.enableCitation"
        active-text="开启引用"
      ></el-switch>
    </div>
    
    <div class="form-section">
      <h4>历史消息窗口大小</h4>
      <el-input-number 
        v-model="nodeData.historySize" 
        :min="1" 
        :max="20"
        :step="1"
        controls-position="right"
        style="width: 100%"
      ></el-input-number>
    </div>

    <!-- 保存按钮 -->
    <div class="settings-actions">
      <el-button type="primary" @click="saveSettings">保存设置</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'GenerationSettings',
  props: {
    initialData: {
      type: Object,
      default: () => ({
        name: '生成回答',
        selectedModel: '默认大模型',
        systemPrompt: '',
        tools: [],
        historySize: 5,
        enableCitation: true
      })
    }
  },
  data() {
    return {
      nodeData: {
        name: '',
        selectedModel: '默认大模型',
        systemPrompt: '',
        tools: [],
        historySize: 5,
        enableCitation: true
      }
    };
  },
  watch: {
    initialData: {
      handler(newVal) {
        this.nodeData = {
          name: newVal.name || '生成回答',
          selectedModel: newVal.selectedModel || '默认大模型',
          systemPrompt: newVal.systemPrompt || '',
          tools: Array.isArray(newVal.tools) ? [...newVal.tools] : [],
          historySize: newVal.historySize || 5,
          enableCitation: newVal.enableCitation !== undefined ? newVal.enableCitation : true
        };
      },
      immediate: true,
      deep: true
    },
    nodeData: {
      handler(newVal) {
        this.$emit('update', newVal);
      },
      deep: true
    }
  },
  methods: {
    // 保存设置
    saveSettings() {
      console.log('[GenerationSettings] 用户点击保存按钮');
      console.log('[GenerationSettings] 保存的数据:', this.nodeData);

      // 构建保存数据
      const saveData = {
        name: this.nodeData.name,
        selectedModel: this.nodeData.selectedModel,
        systemPrompt: this.nodeData.systemPrompt,
        tools: this.nodeData.tools,
        historySize: this.nodeData.historySize,
        enableCitation: this.nodeData.enableCitation
      };

      console.log('[GenerationSettings] 发送保存事件:', saveData);
      this.$emit('save', saveData);

      this.$message({
        message: '设置已保存',
        type: 'success',
        duration: 2000
      });
    },

    handleModelChange(model) {
      this.$emit('model-change', model);
    }
  }
}
</script>

<style scoped>
.form-section {
  margin-bottom: 24px;
}

.form-section h4 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 16px;
  color: #333;
}

.required-marker {
  color: #F56C6C;
  margin-right: 4px;
}

.settings-actions {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
  padding-top: 16px;
  border-top: 1px solid #EBEEF5;
}
</style>