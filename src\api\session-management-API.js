// SESSION MANAGEMENT API 封装
// 这里将封装会话的创建、更新、查询等接口

// TODO: 实现相关API函数 
import ragflowRequest, { RAGFLOW_API_KEY } from './index';

/**
 * 创建 chat assistant 会话
 * @param {string} chatId - chat assistant id
 * @param {Object} data - { name, user_id? }
 * @returns {Promise}
 */
export function createChatSession(chatId, data) {
  return ragflowRequest.post(
    `/api/v1/chats/${chatId}/sessions`,
    data,
    {
      headers: {
        'Authorization': `Bearer ${RAGFLOW_API_KEY}`,
        'Content-Type': 'application/json'
      }
    }
  );
} 

/**
 * 更新 chat assistant 会话
 * @param {string} chatId - chat assistant id
 * @param {string} sessionId - session id
 * @param {Object} data - { name, user_id? }
 * @returns {Promise}
 */
export function updateChatSession(chatId, sessionId, data) {
  return ragflowRequest.put(
    `/api/v1/chats/${chatId}/sessions/${sessionId}`,
    data,
    {
      headers: {
        'Authorization': `Bearer ${RAGFLOW_API_KEY}`,
        'Content-Type': 'application/json'
      }
    }
  );
} 

/**
 * 获取 chat assistant 的会话列表
 * @param {string} chatId - chat assistant id
 * @param {Object} params - 查询参数，如 { page, page_size, orderby, desc, name, id, user_id }
 * @returns {Promise}
 */
export function listChatSessions(chatId, params = {}) {
  return ragflowRequest.get(
    `/api/v1/chats/${chatId}/sessions`,
    {
      params,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${RAGFLOW_API_KEY}`
      }
    }
  );
} 

/**
 * 删除 chat assistant 的会话
 * @param {string} chatId - chat assistant id
 * @param {Array<string>} ids - 要删除的 session id 列表（可选，空则删除全部）
 * @returns {Promise}
 */
export function deleteChatSessions(chatId, ids = []) {
  return ragflowRequest.delete(
    `/api/v1/chats/${chatId}/sessions`,
    {
      data: { ids },
      headers: {
        'Authorization': `Bearer ${RAGFLOW_API_KEY}`,
        'Content-Type': 'application/json'
      }
    }
  );
} 

/**
 * 与 chat assistant 对话（会话管理模块）
 * @param {string} chatId - chat assistant id
 * @param {Object} data - { question, stream, session_id?, user_id? }
 * @returns {Promise}
 */
export function converseWithChatAssistant(chatId, data) {
  return ragflowRequest.post(
    `/api/v1/chats/${chatId}/completions`,
    data,
    {
      headers: {
        'Authorization': `Bearer ${RAGFLOW_API_KEY}`,
        'Content-Type': 'application/json'
      }
    }
  );
} 

/**
 * 与 Agent 对话（原生Agent API）
 * @param {string} agentId - agent id
 * @param {Object} data - 对话参数
 * @param {string} data.question - 用户问题（必需）
 * @param {boolean} [data.stream=false] - 是否流式输出
 * @param {string} [data.session_id] - 会话ID（可选，不提供则创建新会话）
 * @param {string} [data.user_id] - 用户ID（可选，仅在未提供session_id时有效）
 * @param {boolean} [data.sync_dsl=true] - 是否同步DSL（重要：确保使用最新DSL）
 * @param {Object} [otherParams] - Begin组件定义的其他参数
 * @returns {Promise}
 *
 * @example
 * // 创建新会话
 * converseWithAgentNative('agent_id', {
 *   question: '售前',
 *   stream: false,
 *   sync_dsl: true
 * })
 *
 * // 继续现有会话
 * converseWithAgentNative('agent_id', {
 *   question: '详细介绍一下',
 *   stream: false,
 *   session_id: 'existing_session_id',
 *   sync_dsl: true
 * })
 */
export function converseWithAgentNative(agentId, data) {
  // 确保必需参数存在（允许空字符串用于初始化）
  if (data.question === undefined || data.question === null || typeof data.question !== 'string') {
    throw new Error('question参数是必需的，且必须是字符串');
  }

  // 设置默认值
  const requestData = {
    question: data.question,
    stream: data.stream !== undefined ? data.stream : true,
    sync_dsl: data.sync_dsl !== undefined ? data.sync_dsl : true, // 🔥 默认同步DSL
    ...data // 包含其他可能的参数（session_id, user_id等）
  };

  console.log('[原生Agent API] 发送请求:', {
    agentId,
    requestData,
    endpoint: `/api/v1/agents/${agentId}/completions`
  });

  return ragflowRequest.post(
    `/api/v1/agents/${agentId}/completions`,
    requestData,
    {
      headers: {
        'Authorization': `Bearer ${RAGFLOW_API_KEY}`,
        'Content-Type': 'application/json'
      },
      timeout: data.stream ? 120000 : 60000 // 流式输出使用2分钟超时，普通请求1分钟
    }
  );
}

/**
 * 创建 Agent 会话
 * @param {string} agentId - agent id
 * @param {Object} data - 会话创建参数
 * @param {string} [data.user_id] - 用户ID（可选）
 * @param {Object} [otherParams] - Begin组件定义的其他参数
 * @returns {Promise}
 */
export function createAgentSession(agentId, data = {}) {
  const url = data.user_id
    ? `/api/v1/agents/${agentId}/sessions?user_id=${encodeURIComponent(data.user_id)}`
    : `/api/v1/agents/${agentId}/sessions`;

  return ragflowRequest.get(
    url,
    data,
    {
      headers: {
        'Authorization': `Bearer ${RAGFLOW_API_KEY}`,
        'Content-Type': 'application/json'
      }
    }
  );
}

/**
 * 获取 Agent 会话列表
 * @param {string} agentId - agent id
 * @param {Object} params - 查询参数
 * @param {number} [params.page=1] - 页码
 * @param {number} [params.page_size=30] - 每页数量
 * @param {string} [params.orderby='create_time'] - 排序字段
 * @param {boolean} [params.desc=true] - 是否降序
 * @param {string} [params.id] - 会话ID过滤
 * @param {string} [params.user_id] - 用户ID过滤
 * @param {boolean} [params.dsl=true] - 是否包含DSL字段
 * @returns {Promise}
 */
export function listAgentSessions(agentId, params = {}) {
  return ragflowRequest.get(
    `/api/v1/agents/${agentId}/sessions`,
    {
      params,
      headers: {
        'Authorization': `Bearer ${RAGFLOW_API_KEY}`,
        'Content-Type': 'application/json'
      }
    }
  );
}

/**
 * 删除 Agent 会话
 * @param {string} agentId - agent id
 * @param {Array<string>} ids - 要删除的会话ID列表（可选，空则删除全部）
 * @returns {Promise}
 */
export function deleteAgentSessions(agentId, ids = []) {
  return ragflowRequest.delete(
    `/api/v1/agents/${agentId}/sessions`,
    {
      data: { ids },
      headers: {
        'Authorization': `Bearer ${RAGFLOW_API_KEY}`,
        'Content-Type': 'application/json'
      }
    }
  );
}

/**
 * 生成相关问题
 * @param {string} question - 用户原始问题
 * @param {string} loginToken - Bearer 登录Token（24小时有效）
 * @returns {Promise}
 */
export function generateRelatedQuestions(question, loginToken) {
  return ragflowRequest.post(
    `/v1/sessions/related_questions`,
    { question },
    {
      headers: {
        'Authorization': `Bearer ${loginToken}`,
        'Content-Type': 'application/json'
      }
    }
  );
}

