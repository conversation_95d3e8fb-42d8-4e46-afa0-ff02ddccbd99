<template>
  <div class="classification-settings">
    <div class="settings-section">
      <h3 class="section-title">基本设置</h3>
      <el-form label-position="top">
        <el-form-item label="节点名称">
          <el-input v-model="nodeTitle" placeholder="输入节点名称"></el-input>
        </el-form-item>
        
        <el-form-item label="使用模型">
          <el-select v-model="formData.llm_id" placeholder="选择模型" style="width: 100%">
            <el-option label="qwen-max@Tongyi-Qianwen" value="qwen-max@Tongyi-Qianwen"></el-option>
            <el-option label="GPT-4" value="GPT-4"></el-option>
            <el-option label="Claude" value="Claude"></el-option>
            <el-option label="DeepSeek" value="DeepSeek"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="消息历史窗口大小">
          <el-input-number
            v-model="formData.message_history_window_size"
            :min="1"
            :max="50"
            style="width: 100%;"
          ></el-input-number>
        </el-form-item>

        <el-form-item label="温度设置">
          <el-switch v-model="formData.temperatureEnabled" style="margin-bottom: 10px;"></el-switch>
          <el-slider
            v-if="formData.temperatureEnabled"
            v-model="formData.temperature"
            :min="0"
            :max="2"
            :step="0.1"
            :disabled="!formData.temperatureEnabled"
          ></el-slider>
        </el-form-item>
      </el-form>
    </div>
    
    <div class="settings-section">
      <div class="section-header">
        <h3 class="section-title">分类列表</h3>
        <el-button type="primary" size="small" @click="openClassificationDialog">管理分类</el-button>
      </div>
      
      <div v-if="categories.length === 0" class="empty-categories">
        <p>暂无分类，请点击"管理分类"按钮添加</p>
      </div>
      
      <div v-else class="categories-list">
        <div v-for="(category, index) in categories" :key="index" class="category-item">
          <div class="category-info">
            <el-tag class="category-tag" effect="plain" type="warning">{{ category.name }}</el-tag>
            <span class="category-next">→ {{ getNextStepName(category.nextStep) }}</span>
          </div>
          <div class="category-description" v-if="category.description">
            {{ category.description }}
          </div>
        </div>
      </div>
    </div>
    
    <div class="settings-actions">
      <el-button type="primary" @click="saveSettings">保存设置</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ClassificationSettings',
  props: {
    nodeData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      nodeTitle: '',
      selectedModel: 'qwen-max@Tongyi-Qianwen',
      categories: [],
      formData: {
        category_description: {},
        frequencyPenaltyEnabled: false,
        frequency_penalty: 0.7,
        llm_id: "qwen-max@Tongyi-Qianwen",
        maxTokensEnabled: false,
        max_tokens: 256,
        message_history_window_size: 1,
        presencePenaltyEnabled: false,
        presence_penalty: 0.4,
        query: [
          {
            component_id: "Answer:ChubbyWallsBegin",
            type: "reference"
          }
        ],
        temperature: 0.1,
        temperatureEnabled: false,
        topPEnabled: false,
        top_p: 0.3
      },
      nextStepOptions: [
        { label: '知识检索', value: 'retrieval' },
        { label: '生成回答', value: 'generation' },
        { label: '对话', value: 'dialogue' },
        { label: '静态消息', value: 'message' },
        { label: '问题优化', value: 'optimization' },
        { label: '关键词', value: 'keywords' },
        { label: '条件', value: 'conditions' },
        { label: '集线器', value: 'hub' },
        { label: '模板转换', value: 'template' },
        { label: '循环', value: 'loop' },
        { label: '代码', value: 'code' }
      ]
    };
  },
  watch: {
    nodeData: {
      handler(newVal) {
        this.nodeTitle = newVal.title || newVal.name || '问题分类';
        this.selectedModel = newVal.selectedModel || newVal.form?.llm_id || 'qwen-max@Tongyi-Qianwen';
        this.categories = Array.isArray(newVal.categories) ? [...newVal.categories] : [];

        // 初始化 form 数据
        this.formData = {
          category_description: newVal.form?.category_description || {},
          frequencyPenaltyEnabled: newVal.form?.frequencyPenaltyEnabled || false,
          frequency_penalty: newVal.form?.frequency_penalty || 0.7,
          llm_id: newVal.form?.llm_id || newVal.selectedModel || "qwen-max@Tongyi-Qianwen",
          maxTokensEnabled: newVal.form?.maxTokensEnabled || false,
          max_tokens: newVal.form?.max_tokens || 256,
          message_history_window_size: newVal.form?.message_history_window_size || 1,
          presencePenaltyEnabled: newVal.form?.presencePenaltyEnabled || false,
          presence_penalty: newVal.form?.presence_penalty || 0.4,
          query: newVal.form?.query || [
            {
              component_id: "Answer:ChubbyWallsBegin",
              type: "reference"
            }
          ],
          temperature: newVal.form?.temperature || 0.1,
          temperatureEnabled: newVal.form?.temperatureEnabled || false,
          topPEnabled: newVal.form?.topPEnabled || false,
          top_p: newVal.form?.top_p || 0.3
        };

        // 从 categories 构建 category_description
        this.updateCategoryDescription();
      },
      immediate: true,
      deep: true
    },
    categories: {
      handler() {
        this.updateCategoryDescription();
      },
      deep: true
    }
  },
  methods: {
    openClassificationDialog() {
      // 触发父组件中的分类管理对话框
      this.$emit('edit-classification', {
        title: this.nodeTitle,
        categories: this.categories,
        onSave: (title, categories) => {
          this.nodeTitle = title;
          this.categories = categories;
        }
      });
    },
    updateCategoryDescription() {
      // 从 categories 数组构建 category_description 对象
      const categoryDescription = {};
      this.categories.forEach((cat, index) => {
        categoryDescription[cat.name] = {
          index: index,  // 🔥 方案3：始终使用数组索引，确保递增索引
          to: cat.to || cat.nextStep || ''
        };
      });
      this.formData.category_description = categoryDescription;
    },

    saveSettings() {
      // 确保 category_description 是最新的
      this.updateCategoryDescription();

      // 保存设置
      this.$emit('save', {
        title: this.nodeTitle,
        name: this.nodeTitle,
        label: 'Categorize',  // 确保 label 是英文
        selectedModel: this.selectedModel,
        categories: this.categories,
        form: this.formData
      });

      this.$message({
        message: '设置已保存',
        type: 'success',
        duration: 2000
      });
    },
    getNextStepName(value) {
      const option = this.nextStepOptions.find(opt => opt.value === value);
      return option ? option.label : '未设置';
    }
  }
}
</script>

<style scoped>
.classification-settings {
  padding: 10px 0;
}

.settings-section {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 8px;
}

.section-title {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.empty-categories {
  padding: 20px 0;
  text-align: center;
  color: #909399;
  font-size: 14px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.categories-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.category-item {
  padding: 10px;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #ebeef5;
}

.category-info {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.category-tag {
  margin-right: 10px;
}

.category-next {
  color: #666;
  font-size: 13px;
}

.category-description {
  color: #909399;
  font-size: 13px;
  line-height: 1.4;
  margin-top: 5px;
}

.settings-actions {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style> 